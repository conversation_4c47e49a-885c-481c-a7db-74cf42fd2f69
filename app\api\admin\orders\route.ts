import { NextRequest, NextResponse } from 'next/server'
import { getAllOrders, getOrderStats, updateOrderStatus } from '@/lib/orders'

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const limit = searchParams.get('limit')
    const offset = searchParams.get('offset')

    // Prepare filters
    const filters: any = {}
    
    if (status && status !== 'all') {
      filters.status = status
    }
    
    if (limit) {
      filters.limit = parseInt(limit)
    }
    
    if (offset) {
      filters.offset = parseInt(offset)
    }

    // Get orders
    const orders = await getAllOrders(filters)
    
    return NextResponse.json({
      success: true,
      data: orders,
      total: orders.length
    })
  } catch (error) {
    console.error('Admin orders API error:', error)
    return NextResponse.json(
      { error: 'Siparişler yüklenemedi' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { orderId, status, tracking_number } = await request.json()

    if (!orderId || !status) {
      return NextResponse.json(
        { error: 'Sipariş ID ve durum gerekli' },
        { status: 400 }
      )
    }

    // Update order status
    const updatedOrder = await updateOrderStatus(orderId, status)

    // TODO: Handle tracking_number if needed
    // For now, we'll just update the status

    return NextResponse.json({
      success: true,
      data: updatedOrder,
      message: 'Sipariş durumu güncellendi'
    })
  } catch (error) {
    console.error('Admin order update API error:', error)
    return NextResponse.json(
      { error: 'Sipariş güncellenemedi' },
      { status: 500 }
    )
  }
}
