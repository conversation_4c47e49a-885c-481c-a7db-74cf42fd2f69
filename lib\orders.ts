import { supabase, createSupabaseAdmin } from './supabase'
import { Database } from './database.types'

export type Order = Database['public']['Tables']['orders']['Row'] & {
  items?: Database['public']['Tables']['order_items']['Row'][]
  user?: Database['public']['Tables']['profiles']['Row']
}

export type OrderItem = Database['public']['Tables']['order_items']['Row'] & {
  product?: Database['public']['Tables']['products']['Row']
}

// Get user orders
export const getUserOrders = async (userId: string) => {
  const { data, error } = await supabase
    .from('orders')
    .select(`
      *,
      items:order_items(
        *,
        product:products(*)
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false })

  if (error) throw error
  return data as Order[]
}

// Get single order
export const getOrder = async (orderId: string, userId?: string) => {
  const supabaseAdmin = createSupabaseAdmin()

  let query = supabaseAdmin
    .from('orders')
    .select(`
      *,
      items:order_items(
        *,
        product:products(*)
      )
    `)
    .eq('id', orderId)

  if (userId) {
    query = query.eq('user_id', userId)
  }

  const { data: order, error } = await query.single()

  if (error) throw error

  // Get user profile separately
  if (order && order.user_id) {
    const { data: profile } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', order.user_id)
      .single()

    if (profile) {
      order.user = profile
    }
  }

  return order as Order
}

// Create order
export const createOrder = async (orderData: {
  user_id: string
  items: any[]
  shipping_address: any
  billing_address: any
  payment_method: string
  subtotal: number
  shipping_cost: number
  total: number
  notes?: string
}) => {
  const supabaseAdmin = createSupabaseAdmin()
  
  // Generate order number
  const orderNumber = `ORD-${Date.now().toString().slice(-8)}`
  
  // Create order (using existing schema)
  const { data: order, error: orderError } = await supabaseAdmin
    .from('orders')
    .insert({
      user_id: orderData.user_id,
      order_number: orderNumber,
      status: 'pending',
      subtotal: orderData.subtotal,
      total_amount: orderData.total, // Use existing column name
      shipping_amount: orderData.shipping_cost || 0, // Use existing column name
      tax_amount: 0, // Default tax
      currency: 'TRY', // Default currency
      shipping_address: orderData.shipping_address,
      billing_address: orderData.billing_address,
      notes: orderData.notes || ''
    })
    .select()
    .single()

  if (orderError) throw orderError

  // Create order items (using existing schema)
  const orderItems = orderData.items.map(item => ({
    order_id: order.id,
    product_id: item.product_id || item.product?.id,
    quantity: item.quantity,
    price: item.price,
    total: item.price * item.quantity // Calculate total for each item
  }))

  const { error: itemsError } = await supabaseAdmin
    .from('order_items')
    .insert(orderItems)

  if (itemsError) throw itemsError

  return order
}

// Update order status
export const updateOrderStatus = async (orderId: string, status: string) => {
  const supabaseAdmin = createSupabaseAdmin()
  
  const { data, error } = await supabaseAdmin
    .from('orders')
    .update({ 
      status,
      updated_at: new Date().toISOString()
    })
    .eq('id', orderId)
    .select()
    .single()

  if (error) throw error
  return data
}

// Get all orders (admin)
export const getAllOrders = async (filters?: {
  status?: string
  limit?: number
  offset?: number
}) => {
  const supabaseAdmin = createSupabaseAdmin()

  let query = supabaseAdmin
    .from('orders')
    .select(`
      *,
      items:order_items(
        *,
        product:products(*)
      )
    `)
    .order('created_at', { ascending: false })

  if (filters?.status) {
    query = query.eq('status', filters.status)
  }

  if (filters?.limit) {
    query = query.limit(filters.limit)
  }

  if (filters?.offset) {
    query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1)
  }

  const { data: orders, error } = await query

  if (error) throw error

  // Manually fetch user profiles for each order
  if (orders && orders.length > 0) {
    const userIds = [...new Set(orders.map(order => order.user_id).filter(Boolean))]

    if (userIds.length > 0) {
      const { data: profiles } = await supabaseAdmin
        .from('profiles')
        .select('*')
        .in('id', userIds)

      // Attach user profiles to orders
      const ordersWithUsers = orders.map(order => ({
        ...order,
        user: profiles?.find(profile => profile.id === order.user_id) || null
      }))

      return ordersWithUsers as Order[]
    }
  }

  return orders as Order[]
}

// Get order statistics
export const getOrderStats = async () => {
  const supabaseAdmin = createSupabaseAdmin()
  
  const { data, error } = await supabaseAdmin
    .from('orders')
    .select('status, total_amount, created_at')

  if (error) throw error

  const stats = {
    total_orders: data.length,
    total_revenue: data.reduce((sum, order) => sum + (order.total_amount || 0), 0),
    pending_orders: data.filter(order => order.status === 'pending').length,
    confirmed_orders: data.filter(order => order.status === 'confirmed').length,
    shipped_orders: data.filter(order => order.status === 'shipped').length,
    delivered_orders: data.filter(order => order.status === 'delivered').length,
    cancelled_orders: data.filter(order => order.status === 'cancelled').length,
    today_orders: data.filter(order => {
      const today = new Date().toDateString()
      const orderDate = new Date(order.created_at).toDateString()
      return today === orderDate
    }).length,
    this_month_revenue: data.filter(order => {
      const thisMonth = new Date().getMonth()
      const orderMonth = new Date(order.created_at).getMonth()
      return thisMonth === orderMonth
    }).reduce((sum, order) => sum + (order.total_amount || 0), 0)
  }

  return stats
}

// Cancel order
export const cancelOrder = async (orderId: string, userId?: string) => {
  const supabaseAdmin = createSupabaseAdmin()
  
  let query = supabaseAdmin
    .from('orders')
    .update({ 
      status: 'cancelled',
      updated_at: new Date().toISOString()
    })
    .eq('id', orderId)

  if (userId) {
    query = query.eq('user_id', userId)
  }

  const { data, error } = await query.select().single()

  if (error) throw error
  return data
}
