'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { getCategories } from '@/lib/products'
import { generateSlug } from '@/lib/csv-import'
import { toast } from 'react-hot-toast'
import { ArrowLeft, Save, Upload, X, Loader2 } from 'lucide-react'

export default function EditProductPage() {
  const router = useRouter()
  const params = useParams()
  const productId = params.id as string
  
  const [categories, setCategories] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  
  const [productData, setProductData] = useState({
    name: '',
    slug: '',
    description: '',
    short_description: '',
    price: '',
    compare_price: '',
    sku: '',
    category_id: '',
    material_type: 'PLA+',
    dimensions: '',
    weight_grams: '',
    is_active: true,
    is_featured: false,
    seo_title: '',
    seo_description: ''
  })

  const [images, setImages] = useState<string[]>([''])
  const [inventory, setInventory] = useState({
    quantity: '',
    reserved_quantity: '0',
    reorder_level: '10'
  })

  useEffect(() => {
    loadCategories()
    loadProduct()
  }, [productId])

  const loadCategories = async () => {
    try {
      const categoriesData = await getCategories()
      setCategories(categoriesData)
    } catch (error) {
      console.error('Error loading categories:', error)
      toast.error('Kategoriler yüklenemedi')
    }
  }

  const loadProduct = async () => {
    try {
      const response = await fetch(`/api/admin/products/${productId}`)
      if (!response.ok) {
        throw new Error('Product not found')
      }
      
      const result = await response.json()
      const product = result.data
      
      // Set product data
      setProductData({
        name: product.name || '',
        slug: product.slug || '',
        description: product.description || '',
        short_description: product.short_description || '',
        price: product.price?.toString() || '',
        compare_price: product.compare_price?.toString() || '',
        sku: product.sku || '',
        category_id: product.category_id || '',
        material_type: product.material_type || 'PLA+',
        dimensions: product.dimensions || '',
        weight_grams: product.weight_grams?.toString() || '',
        is_active: product.is_active ?? true,
        is_featured: product.is_featured ?? false,
        seo_title: product.seo_title || '',
        seo_description: product.seo_description || ''
      })

      // Set images
      if (product.images && product.images.length > 0) {
        const imageUrls = product.images
          .sort((a: any, b: any) => a.sort_order - b.sort_order)
          .map((img: any) => img.image_url)
        setImages(imageUrls.length > 0 ? imageUrls : [''])
      }

      // Set inventory
      if (product.inventory) {
        setInventory({
          quantity: product.inventory.quantity?.toString() || '0',
          reserved_quantity: product.inventory.reserved_quantity?.toString() || '0',
          reorder_level: product.inventory.reorder_level?.toString() || '10'
        })
      }
      
    } catch (error) {
      console.error('Error loading product:', error)
      toast.error('Ürün yüklenemedi')
      router.push('/admin/products')
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setProductData(prev => ({
      ...prev,
      [field]: value
    }))

    // Auto-generate slug when name changes
    if (field === 'name' && value) {
      setProductData(prev => ({
        ...prev,
        slug: generateSlug(value)
      }))
    }
  }

  const handleImageChange = (index: number, value: string) => {
    const newImages = [...images]
    newImages[index] = value
    setImages(newImages)
  }

  const addImageField = () => {
    setImages([...images, ''])
  }

  const removeImageField = (index: number) => {
    if (images.length > 1) {
      const newImages = images.filter((_, i) => i !== index)
      setImages(newImages)
    }
  }

  const handleInventoryChange = (field: string, value: string) => {
    setInventory(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!productData.name.trim()) {
      toast.error('Ürün adı gereklidir')
      return
    }

    if (!productData.price || parseFloat(productData.price) <= 0) {
      toast.error('Geçerli bir fiyat giriniz')
      return
    }

    if (!productData.category_id) {
      toast.error('Kategori seçiniz')
      return
    }

    setIsSaving(true)
    try {
      // Prepare product data
      const validImages = images.filter(img => img.trim() !== '')
      
      const updateData = {
        name: productData.name.trim(),
        slug: productData.slug.trim(),
        description: productData.description.trim(),
        short_description: productData.short_description.trim(),
        price: parseFloat(productData.price),
        compare_price: productData.compare_price ? parseFloat(productData.compare_price) : null,
        sku: productData.sku.trim(),
        category_id: productData.category_id,
        material_type: productData.material_type,
        dimensions: productData.dimensions.trim(),
        weight_grams: productData.weight_grams ? parseInt(productData.weight_grams) : null,
        is_active: productData.is_active,
        is_featured: productData.is_featured,
        seo_title: productData.seo_title.trim(),
        seo_description: productData.seo_description.trim()
      }

      // Update product via API
      const response = await fetch(`/api/admin/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...updateData,
          images: validImages,
          inventory: {
            quantity: inventory.quantity,
            reserved_quantity: inventory.reserved_quantity,
            reorder_level: inventory.reorder_level
          }
        })
      })

      if (response.ok) {
        toast.success('Ürün başarıyla güncellendi')
        router.push('/admin/products')
      } else {
        const error = await response.json()
        throw new Error(error.message || 'Ürün güncellenemedi')
      }
    } catch (error) {
      console.error('Error updating product:', error)
      toast.error(error instanceof Error ? error.message : 'Ürün güncellenemedi')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Ürün yükleniyor...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Ürün Düzenle</h1>
            <p className="text-gray-600 mt-2">
              Ürün bilgilerini güncelleyin
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Temel Bilgiler</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Ürün Adı *</Label>
                  <Input
                    id="name"
                    value={productData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Ürün adını giriniz"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="slug">URL Slug</Label>
                  <Input
                    id="slug"
                    value={productData.slug}
                    onChange={(e) => handleInputChange('slug', e.target.value)}
                    placeholder="url-slug"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="short_description">Kısa Açıklama</Label>
                <Textarea
                  id="short_description"
                  value={productData.short_description}
                  onChange={(e) => handleInputChange('short_description', e.target.value)}
                  placeholder="Ürünün kısa açıklaması"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="description">Detaylı Açıklama</Label>
                <Textarea
                  id="description"
                  value={productData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Ürünün detaylı açıklaması"
                  rows={6}
                />
              </div>
            </CardContent>
          </Card>

          {/* Pricing & Inventory */}
          <Card>
            <CardHeader>
              <CardTitle>Fiyat ve Stok</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="price">Fiyat (₺) *</Label>
                  <Input
                    id="price"
                    type="number"
                    step="0.01"
                    min="0"
                    value={productData.price}
                    onChange={(e) => handleInputChange('price', e.target.value)}
                    placeholder="0.00"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="compare_price">Karşılaştırma Fiyatı (₺)</Label>
                  <Input
                    id="compare_price"
                    type="number"
                    step="0.01"
                    min="0"
                    value={productData.compare_price}
                    onChange={(e) => handleInputChange('compare_price', e.target.value)}
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <Label htmlFor="sku">Stok Kodu (SKU)</Label>
                  <Input
                    id="sku"
                    value={productData.sku}
                    onChange={(e) => handleInputChange('sku', e.target.value)}
                    placeholder="SKU123"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="quantity">Stok Miktarı</Label>
                  <Input
                    id="quantity"
                    type="number"
                    min="0"
                    value={inventory.quantity}
                    onChange={(e) => handleInventoryChange('quantity', e.target.value)}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="reserved_quantity">Rezerve Miktar</Label>
                  <Input
                    id="reserved_quantity"
                    type="number"
                    min="0"
                    value={inventory.reserved_quantity}
                    onChange={(e) => handleInventoryChange('reserved_quantity', e.target.value)}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="reorder_level">Yeniden Sipariş Seviyesi</Label>
                  <Input
                    id="reorder_level"
                    type="number"
                    min="0"
                    value={inventory.reorder_level}
                    onChange={(e) => handleInventoryChange('reorder_level', e.target.value)}
                    placeholder="10"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Product Details */}
          <Card>
            <CardHeader>
              <CardTitle>Ürün Detayları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="material_type">Malzeme Türü</Label>
                  <Select value={productData.material_type} onValueChange={(value) => handleInputChange('material_type', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Malzeme seçiniz" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PLA">PLA</SelectItem>
                      <SelectItem value="PLA+">PLA+</SelectItem>
                      <SelectItem value="ABS">ABS</SelectItem>
                      <SelectItem value="PETG">PETG</SelectItem>
                      <SelectItem value="TPU">TPU</SelectItem>
                      <SelectItem value="Wood">Wood</SelectItem>
                      <SelectItem value="Metal">Metal</SelectItem>
                      <SelectItem value="Other">Diğer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="dimensions">Boyutlar</Label>
                  <Input
                    id="dimensions"
                    value={productData.dimensions}
                    onChange={(e) => handleInputChange('dimensions', e.target.value)}
                    placeholder="20x15x10 cm"
                  />
                </div>
                <div>
                  <Label htmlFor="weight_grams">Ağırlık (gram)</Label>
                  <Input
                    id="weight_grams"
                    type="number"
                    min="0"
                    value={productData.weight_grams}
                    onChange={(e) => handleInputChange('weight_grams', e.target.value)}
                    placeholder="100"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Product Images */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Ürün Görselleri
                <Button type="button" variant="outline" size="sm" onClick={addImageField}>
                  <Upload className="mr-2 h-4 w-4" />
                  Görsel Ekle
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {images.map((image, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="flex-1">
                    <Label htmlFor={`image-${index}`}>
                      Görsel URL {index + 1} {index === 0 && '(Ana Görsel)'}
                    </Label>
                    <Input
                      id={`image-${index}`}
                      value={image}
                      onChange={(e) => handleImageChange(index, e.target.value)}
                      placeholder="https://example.com/image.jpg"
                    />
                  </div>
                  {images.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeImageField(index)}
                      className="mt-6"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>

          {/* SEO */}
          <Card>
            <CardHeader>
              <CardTitle>SEO Ayarları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="seo_title">SEO Başlık</Label>
                <Input
                  id="seo_title"
                  value={productData.seo_title}
                  onChange={(e) => handleInputChange('seo_title', e.target.value)}
                  placeholder="SEO için başlık"
                  maxLength={60}
                />
                <p className="text-sm text-gray-500 mt-1">
                  {productData.seo_title.length}/60 karakter
                </p>
              </div>
              <div>
                <Label htmlFor="seo_description">SEO Açıklama</Label>
                <Textarea
                  id="seo_description"
                  value={productData.seo_description}
                  onChange={(e) => handleInputChange('seo_description', e.target.value)}
                  placeholder="SEO için açıklama"
                  rows={3}
                  maxLength={160}
                />
                <p className="text-sm text-gray-500 mt-1">
                  {productData.seo_description.length}/160 karakter
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status */}
          <Card>
            <CardHeader>
              <CardTitle>Durum</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_active"
                  checked={productData.is_active}
                  onCheckedChange={(checked) => handleInputChange('is_active', checked)}
                />
                <Label htmlFor="is_active">Aktif</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_featured"
                  checked={productData.is_featured}
                  onCheckedChange={(checked) => handleInputChange('is_featured', checked)}
                />
                <Label htmlFor="is_featured">Öne Çıkan</Label>
              </div>
            </CardContent>
          </Card>

          {/* Category */}
          <Card>
            <CardHeader>
              <CardTitle>Kategori</CardTitle>
            </CardHeader>
            <CardContent>
              <Label htmlFor="category">Kategori Seçiniz *</Label>
              <Select value={productData.category_id} onValueChange={(value) => handleInputChange('category_id', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Kategori seçiniz" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <Button type="submit" className="w-full" disabled={isSaving}>
                  <Save className="mr-2 h-4 w-4" />
                  {isSaving ? 'Güncelleniyor...' : 'Ürünü Güncelle'}
                </Button>

                <Button type="button" variant="outline" className="w-full" onClick={() => router.back()}>
                  İptal
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </form>
    </div>
  )
}
