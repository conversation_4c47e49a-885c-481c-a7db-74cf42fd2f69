/*
  # Site Settings Table
  
  1. New Table
    - `site_settings` - Store site-wide configuration settings
  
  2. Security
    - Enable RLS on site_settings table
    - Only admins can read/write settings
*/

-- Site settings table for storing configuration
CREATE TABLE IF NOT EXISTS site_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  key text UNIQUE NOT NULL,
  value jsonb NOT NULL,
  description text DEFAULT '',
  category text DEFAULT 'general',
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE site_settings ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "<PERSON><PERSON> can view all settings" ON site_settings
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Ad<PERSON> can insert settings" ON site_settings
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Ad<PERSON> can update settings" ON site_settings
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Admins can delete settings" ON site_settings
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Insert default shipping settings
INSERT INTO site_settings (key, value, description, category) VALUES
  ('shipping_cost', '{"amount": 29.90, "currency": "TRY"}', 'Default shipping cost', 'shipping'),
  ('free_shipping_threshold', '{"amount": 500, "currency": "TRY"}', 'Minimum order amount for free shipping', 'shipping'),
  ('shipping_enabled', 'true', 'Enable/disable shipping', 'shipping'),
  ('site_name', '"3Dünyam"', 'Site name', 'general'),
  ('site_description', '"3D Baskı Ürünleri E-Ticaret Platformu"', 'Site description', 'general'),
  ('contact_email', '"<EMAIL>"', 'Contact email address', 'general'),
  ('contact_phone', '"+90 ************"', 'Contact phone number', 'general')
ON CONFLICT (key) DO NOTHING;

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_site_settings_key ON site_settings(key);
CREATE INDEX IF NOT EXISTS idx_site_settings_category ON site_settings(category);
CREATE INDEX IF NOT EXISTS idx_site_settings_active ON site_settings(is_active);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_site_settings_updated_at 
  BEFORE UPDATE ON site_settings 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();
