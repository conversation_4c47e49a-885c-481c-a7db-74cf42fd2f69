'use client'

import { useEffect, useState, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { XCircle, ArrowLeft, RefreshCw, Home, CreditCard } from 'lucide-react'
import { getShopierText } from '@/lib/shopier'

function PaymentErrorContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [errorDetails, setErrorDetails] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  const orderId = searchParams.get('order_id')
  const errorMessage = searchParams.get('error_message')
  const errorCode = searchParams.get('error_code')

  useEffect(() => {
    // Load error details
    setErrorDetails({
      orderId,
      errorMessage: errorMessage || getShopierText('An error occurred in payment.The transaction has been declined.', 'tr'),
      errorCode: errorCode || 'PAYMENT_FAILED',
      timestamp: new Date().toISOString()
    })
    setIsLoading(false)
  }, [orderId, errorMessage, errorCode])

  const handleRetryPayment = () => {
    if (orderId) {
      router.push(`/checkout?order_id=${orderId}`)
    } else {
      router.push('/cart')
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <div className="animate-pulse space-y-4">
            <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto"></div>
            <div className="h-8 bg-gray-200 rounded w-3/4 mx-auto"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-2xl mx-auto text-center">
        {/* Error Icon */}
        <div className="mb-8">
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <XCircle className="h-12 w-12 text-red-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Ödeme Başarısız
          </h1>
          <p className="text-gray-600 text-lg">
            Ödeme işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.
          </p>
        </div>

        {/* Error Details */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center justify-center space-x-2 text-red-600">
              <CreditCard className="h-5 w-5" />
              <span>Hata Detayları</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-red-50 p-4 rounded-lg">
              <p className="text-red-800 font-medium mb-2">
                {errorDetails?.errorMessage}
              </p>
              {errorDetails?.errorCode && (
                <p className="text-red-600 text-sm">
                  Hata Kodu: {errorDetails.errorCode}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              {errorDetails?.orderId && (
                <div>
                  <span className="text-gray-600">Sipariş ID:</span>
                  <p className="font-medium">{errorDetails.orderId}</p>
                </div>
              )}
              
              <div>
                <span className="text-gray-600">Hata Zamanı:</span>
                <p className="font-medium">
                  {new Date(errorDetails?.timestamp).toLocaleString('tr-TR')}
                </p>
              </div>
              
              <div>
                <span className="text-gray-600">Ödeme Yöntemi:</span>
                <p className="font-medium">Kredi Kartı (Shopier)</p>
              </div>
              
              <div>
                <span className="text-gray-600">Durum:</span>
                <p className="font-medium text-red-600">Başarısız</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Common Error Causes */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Olası Nedenler</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-left space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-gray-600">
                  Kart limitiniz yetersiz olabilir
                </p>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-gray-600">
                  Kart bilgileri hatalı girilmiş olabilir
                </p>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-gray-600">
                  3D Secure doğrulama başarısız olmuş olabilir
                </p>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-gray-600">
                  Bankanız işlemi reddetmiş olabilir
                </p>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-gray-600">
                  İnternet bağlantısı kesilmiş olabilir
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button onClick={handleRetryPayment} size="lg">
              <RefreshCw className="mr-2 h-5 w-5" />
              Tekrar Dene
            </Button>
            
            <Button asChild variant="outline" size="lg">
              <Link href="/cart">
                <ArrowLeft className="mr-2 h-5 w-5" />
                Sepete Dön
              </Link>
            </Button>
            
            <Button asChild variant="outline" size="lg">
              <Link href="/">
                <Home className="mr-2 h-5 w-5" />
                Ana Sayfa
              </Link>
            </Button>
          </div>
        </div>

        {/* Help Section */}
        <div className="mt-12 p-6 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">
            Yardıma mı İhtiyacınız Var?
          </h3>
          <div className="text-sm text-blue-800 space-y-2">
            <p>
              Sorun devam ederse lütfen müşteri hizmetlerimizle iletişime geçin:
            </p>
            <div className="flex flex-col sm:flex-row gap-4 mt-4">
              <Button asChild variant="outline" size="sm">
                <Link href="/contact">
                  İletişim
                </Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="tel:+905551234567">
                  Telefon: +90 555 123 45 67
                </Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="mailto:<EMAIL>">
                  E-posta: <EMAIL>
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function PaymentErrorPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <div className="animate-pulse space-y-4">
            <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto"></div>
            <div className="h-8 bg-gray-200 rounded w-3/4 mx-auto"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
          </div>
        </div>
      </div>
    }>
      <PaymentErrorContent />
    </Suspense>
  )
}
