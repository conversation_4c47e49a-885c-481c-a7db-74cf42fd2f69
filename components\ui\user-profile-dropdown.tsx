"use client"
import React, { useState, useEffect, useRef, ReactNode } from 'react';
import Link from 'next/link';
import { User, Settings, Package, Heart, LogOut } from 'lucide-react';

// Dropdown Menu Components
interface DropdownMenuProps {
    children: ReactNode;
    trigger: ReactNode;
}

const UserProfileDropdown = ({ children, trigger }: DropdownMenuProps) => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleTriggerClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        setIsOpen(!isOpen);
    }

    return (
        <div className="relative inline-block text-left" ref={dropdownRef}>
            <div onClick={handleTriggerClick} className="cursor-pointer">
                {trigger}
            </div>
            {isOpen && (
                <div 
                    className="origin-top-right absolute right-0 mt-2 w-64 rounded-2xl shadow-xl bg-white dark:bg-zinc-900 ring-1 ring-black ring-opacity-5 focus:outline-none z-50 animate-in fade-in-0 zoom-in-95 p-2"
                    role="menu" 
                    aria-orientation="vertical"
                >
                    {children}
                </div>
            )}
        </div>
    );
};

interface DropdownMenuItemProps {
    children: ReactNode;
    onClick?: () => void;
    active?: boolean;
    href?: string;
}

const UserProfileDropdownItem = ({ children, onClick, active = false, href }: DropdownMenuItemProps) => {
    const content = (
        <div
            onClick={(e: React.MouseEvent) => {
                if (!href) {
                    e.preventDefault();
                }
                if(onClick) onClick();
            }}
            className={`
                text-zinc-800 dark:text-zinc-200 font-medium group flex items-center 
                px-3 py-2.5 text-sm rounded-lg transition-colors duration-150 cursor-pointer
                ${active 
                    ? 'bg-zinc-100 dark:bg-zinc-800' 
                    : 'hover:bg-zinc-100 dark:hover:bg-zinc-800'
                }
            `}
            role="menuitem"
        >
            {children}
        </div>
    );

    if (href) {
        return <Link href={href}>{content}</Link>;
    }

    return content;
};

const UserProfileDropdownSeparator = () => (
    <div className="my-2 h-px bg-zinc-200 dark:bg-zinc-700" />
);

// User Profile Header Component
interface UserProfileHeaderProps {
    user: {
        first_name: string;
        last_name: string;
        email: string;
    };
}

const UserProfileHeader = ({ user }: UserProfileHeaderProps) => (
    <div className="px-3 py-3 border-b border-zinc-200 dark:border-zinc-700">
        <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold text-sm">
                    {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                </span>
            </div>
            <div className="flex-1 min-w-0">
                <p className="text-sm font-semibold text-zinc-900 dark:text-zinc-100 truncate">
                    {user.first_name} {user.last_name}
                </p>
                <p className="text-xs text-zinc-500 dark:text-zinc-400 truncate">
                    {user.email}
                </p>
            </div>
        </div>
    </div>
);

export { 
    UserProfileDropdown, 
    UserProfileDropdownItem, 
    UserProfileDropdownSeparator,
    UserProfileHeader 
};
