-- Fix orders table structure to match application requirements

-- Add missing payment_method column
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS payment_method text DEFAULT 'cash-on-delivery';

-- Add missing shipping_cost column (rename shipping_amount)
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS shipping_cost decimal(10,2) DEFAULT 0;

-- Add missing total column (rename total_amount)  
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS total decimal(10,2);

-- Update existing data to use new columns
UPDATE orders 
SET 
  shipping_cost = COALESCE(shipping_amount, 0),
  total = COALESCE(total_amount, subtotal + COALESCE(tax_amount, 0) + COALESCE(shipping_amount, 0))
WHERE shipping_cost IS NULL OR total IS NULL;

-- Drop old columns if they exist and new columns are populated
-- (We'll keep them for now to avoid data loss)

-- Add constraints
ALTER TABLE orders 
ADD CONSTRAINT check_total_positive CHECK (total > 0);

ALTER TABLE orders 
ADD CONSTRAINT check_subtotal_positive CHECK (subtotal > 0);

-- Add index for payment method
CREATE INDEX IF NOT EXISTS idx_orders_payment_method ON orders(payment_method);

-- Update order_status enum if needed (already exists)
-- The enum should include: 'pending', 'confirmed', 'shipped', 'delivered', 'cancelled'

-- Add order_number generation function if not exists
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS text AS $$
BEGIN
    RETURN 'ORD-' || LPAD(EXTRACT(epoch FROM now())::text, 10, '0') || '-' || LPAD((RANDOM() * 999)::int::text, 3, '0');
END;
$$ LANGUAGE plpgsql;

-- Add trigger to auto-generate order number if not provided
CREATE OR REPLACE FUNCTION set_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := generate_order_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_set_order_number ON orders;
CREATE TRIGGER trigger_set_order_number
    BEFORE INSERT ON orders
    FOR EACH ROW
    EXECUTE FUNCTION set_order_number();

-- Ensure updated_at trigger exists
DROP TRIGGER IF EXISTS update_orders_updated_at ON orders;
CREATE TRIGGER update_orders_updated_at
    BEFORE UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
