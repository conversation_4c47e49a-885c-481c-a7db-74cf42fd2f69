<?php
$shopierText = array();
$shopierText['Pay with Credit Card'] = 'Kredi Kartı ile Ödeme';
$shopierText['Enable/Disable'] = 'Etkinleştir/Pasifleştir';
$shopierText['Enable Shopier Module'] = 'Shopier Etkinleştir';
$shopierText['Title:'] = 'Başlık:';
$shopierText['This controls the title which the user sees during checkout.'] = 'Kullanıcıların ödeme esnasında gördükleri başlık mesajı buraya yazılır.';
$shopierText['Description:'] = 'Açıklama:';
$shopierText['This controls the description which the user sees during checkout.'] = 'Kullanıcıların ödeme esnasında gördükleri açıklama mesajı buraya yazılır.';
$shopierText['Pay securely by Shopier Module.'] = 'Kredi kartı ile güvenle ödeyin.';
$shopierText['API Key'] = 'API Kullanıcı';
$shopierText['This obtained by user from Shopier panel'] = 'Shopier Panelinden erişelebilir';
$shopierText['Secret'] = 'API Şifre';
$shopierText['Payment Endpoint URL'] = 'Ödeme URL';
$shopierText['Shipping Endpoint URL'] = 'Gönderi URL';
$shopierText['Cancel Endpoint URL'] = 'İptal URL';
$shopierText['Response URL'] = 'Geri Dönüş URL';
$shopierText['I certify that I have provided Shopier with the proper Response URL:'] = 'Bu URL adresini Shopier panelinde doğru şekilde tanımlayacağımı taahhüt ederim:';
$shopierText['Thank you for your order, please click the button below to pay with Shopier Module.'] = 'Siparişiniz için teşekkürler, kredi kartı ile güvenli ödeme yapmak için tıklayınız.';
$shopierText['Pay via Shopier'] = 'Kredi kartı ile öde';
$shopierText['Cancel order & restore cart'] = 'Siparişi iptal edin';
$shopierText['Thank you for your order. We are now redirecting you to Payment Gateway to make payment'] = 'Siparişiniz için teşekkürler, ödeme sayfasına yönlendiriliyorsunuz';
$shopierText['Thank you for shopping with us. Your account has been charged and your transaction is successful. We will be shipping your order to you soon.'] = 'Siparişiniz için teşekkürler, ödemeniz alınmıştır.';
$shopierText['Shopier payment successful'] = 'Shopier ödeme başarılı';
$shopierText['Payment Id from Shopier: '] = 'Shopier sipariş numarası: ';
$shopierText['Thank you for shopping with us. However, the transaction has been declined.'] = 'İşlem reddedilmiştir.';
$shopierText['Transaction Declined: '] = 'İşlem reddedilmiştir: ';
$shopierText['Security Error. Illegal access detected'] = 'Güvenlik hatası, yetkisiz erişim!';
$shopierText['AN ERROR OCCURED IN PAYMENT'] = 'Ödeme esnasında bir hata olmuştur';
$shopierText['An error occurred in payment.The transaction has been declined.'] = 'Ödeme esnasında bir hata oluştu, işlem reddedildi.';
$shopierText['If you are only using it on one site, this field should be 1. If you use more than 1 site, follow the setup guide for setting this field.'] = 'Sadece tek bir sitede kullanıyorsanız, bu alan 1 olmalıdır. 1 den fazla sitede kullanıyorsanız, bu alanın ayarı için kurulum kılavuzunu takip ediniz.';
$shopierText['In standard usage, you don\'t need to change this field.'] = 'Standart kullanımda, bu alanı değiştirmeniz gerekmemektedir.';
$shopierText['Use Adress'] = 'Adres Kullanımı';
$shopierText['Use Billing Address'] = 'Fatura Adresini Kullan';
$shopierText['Use Delivery Address'] = 'Teslimat Adresini Kullan';