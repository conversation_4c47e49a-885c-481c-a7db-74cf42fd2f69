# Database Schema Documentation

## 1. Overview

The 3Dünyam platform uses PostgreSQL as the primary database, hosted on Supabase. The schema is designed to support a full-featured e-commerce platform with specific considerations for 3D printed products.

### Key Design Principles
- **Normalization**: Proper normalization to reduce data redundancy
- **Performance**: Strategic indexing for optimal query performance
- **Security**: Row Level Security (RLS) policies for data protection
- **Scalability**: UUID primary keys and efficient foreign key relationships
- **Audit Trail**: Created/updated timestamps on all entities

## 2. Entity Relationship Diagram

```mermaid
erDiagram
    profiles ||--o{ orders : "places"
    profiles ||--o{ carts : "has"
    categories ||--o{ products : "contains"
    products ||--o{ product_images : "has"
    products ||--|| inventory : "tracks"
    products ||--o{ cart_items : "references"
    products ||--o{ order_items : "references"
    carts ||--o{ cart_items : "contains"
    orders ||--o{ order_items : "contains"

    profiles {
        uuid id PK
        text email UK
        text first_name
        text last_name
        text phone
        user_role role
        text avatar_url
        text address_line1
        text address_line2
        text city
        text postal_code
        text country
        timestamptz created_at
        timestamptz updated_at
    }

    categories {
        uuid id PK
        text name UK
        text slug UK
        text description
        text image_url
        boolean is_active
        int sort_order
        timestamptz created_at
        timestamptz updated_at
    }

    products {
        uuid id PK
        text name
        text slug UK
        text description
        text short_description
        decimal price
        decimal compare_price
        text sku UK
        uuid category_id FK
        text material_type
        text dimensions
        int weight_grams
        boolean is_active
        boolean is_featured
        text seo_title
        text seo_description
        timestamptz created_at
        timestamptz updated_at
    }

    product_images {
        uuid id PK
        uuid product_id FK
        text image_url
        text alt_text
        int sort_order
        boolean is_primary
        timestamptz created_at
    }

    inventory {
        uuid id PK
        uuid product_id FK UK
        int quantity
        int reserved_quantity
        int reorder_level
        timestamptz updated_at
    }

    carts {
        uuid id PK
        uuid user_id FK UK
        timestamptz created_at
        timestamptz updated_at
    }

    cart_items {
        uuid id PK
        uuid cart_id FK
        uuid product_id FK
        int quantity
        decimal price
        timestamptz created_at
        timestamptz updated_at
    }

    orders {
        uuid id PK
        text order_number UK
        uuid user_id FK
        order_status status
        decimal subtotal
        decimal shipping_cost
        decimal total
        jsonb shipping_address
        jsonb billing_address
        text payment_method
        text notes
        timestamptz created_at
        timestamptz updated_at
    }

    order_items {
        uuid id PK
        uuid order_id FK
        uuid product_id FK
        int quantity
        decimal price
        text product_name
        text product_sku
        timestamptz created_at
    }
```

## 3. Table Definitions

### 3.1 profiles
Extends Supabase auth.users with additional profile information.

```sql
CREATE TABLE profiles (
    id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email text UNIQUE NOT NULL,
    first_name text NOT NULL DEFAULT '',
    last_name text NOT NULL DEFAULT '',
    phone text DEFAULT '',
    role user_role DEFAULT 'customer',
    avatar_url text,
    address_line1 text DEFAULT '',
    address_line2 text DEFAULT '',
    city text DEFAULT '',
    postal_code text DEFAULT '',
    country text DEFAULT 'Turkey',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);
```

**Constraints:**
- Primary key references auth.users(id)
- Email must be unique
- Role defaults to 'customer'

**Indexes:**
```sql
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_profiles_role ON profiles(role);
```

### 3.2 categories
Product categories for organization and navigation.

```sql
CREATE TABLE categories (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text UNIQUE NOT NULL,
    slug text UNIQUE NOT NULL,
    description text DEFAULT '',
    image_url text,
    is_active boolean DEFAULT true,
    sort_order integer DEFAULT 0,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);
```

**Constraints:**
- Name and slug must be unique
- Sort order for display ordering

**Indexes:**
```sql
CREATE INDEX idx_categories_slug ON categories(slug);
CREATE INDEX idx_categories_active ON categories(is_active);
CREATE INDEX idx_categories_sort ON categories(sort_order);
```

### 3.3 products
Core product information with 3D printing specific fields.

```sql
CREATE TABLE products (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    slug text UNIQUE NOT NULL,
    description text DEFAULT '',
    short_description text DEFAULT '',
    price decimal(10,2) NOT NULL,
    compare_price decimal(10,2),
    sku text UNIQUE NOT NULL,
    category_id uuid REFERENCES categories(id) ON DELETE SET NULL,
    material_type text DEFAULT 'PLA+',
    dimensions text,
    weight_grams integer,
    is_active boolean DEFAULT true,
    is_featured boolean DEFAULT false,
    seo_title text,
    seo_description text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);
```

**Constraints:**
- SKU must be unique
- Price must be positive
- Compare price must be greater than price if set

**Indexes:**
```sql
CREATE INDEX idx_products_slug ON products(slug);
CREATE INDEX idx_products_sku ON products(sku);
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_active ON products(is_active);
CREATE INDEX idx_products_featured ON products(is_featured);
CREATE INDEX idx_products_price ON products(price);
CREATE INDEX idx_products_active_featured ON products(is_active, is_featured);
```

### 3.4 product_images
Multiple images per product with ordering and primary image designation.

```sql
CREATE TABLE product_images (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id uuid NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    image_url text NOT NULL,
    alt_text text DEFAULT '',
    sort_order integer DEFAULT 0,
    is_primary boolean DEFAULT false,
    created_at timestamptz DEFAULT now()
);
```

**Constraints:**
- Only one primary image per product
- Sort order for image display sequence

**Indexes:**
```sql
CREATE INDEX idx_product_images_product_id ON product_images(product_id);
CREATE INDEX idx_product_images_primary ON product_images(product_id, is_primary);
```

### 3.5 inventory
Stock tracking for products.

```sql
CREATE TABLE inventory (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id uuid UNIQUE NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    quantity integer NOT NULL DEFAULT 0,
    reserved_quantity integer NOT NULL DEFAULT 0,
    reorder_level integer NOT NULL DEFAULT 10,
    updated_at timestamptz DEFAULT now()
);
```

**Constraints:**
- One inventory record per product
- Quantities must be non-negative
- Available quantity = quantity - reserved_quantity

**Indexes:**
```sql
CREATE INDEX idx_inventory_product_id ON inventory(product_id);
CREATE INDEX idx_inventory_low_stock ON inventory(quantity, reorder_level);
```

### 3.6 carts
Shopping carts for users.

```sql
CREATE TABLE carts (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid UNIQUE NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);
```

**Constraints:**
- One cart per user
- Cascade delete when user is deleted

**Indexes:**
```sql
CREATE INDEX idx_carts_user_id ON carts(user_id);
```

### 3.7 cart_items
Items within shopping carts.

```sql
CREATE TABLE cart_items (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    cart_id uuid NOT NULL REFERENCES carts(id) ON DELETE CASCADE,
    product_id uuid NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    quantity integer NOT NULL DEFAULT 1,
    price decimal(10,2) NOT NULL,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    UNIQUE(cart_id, product_id)
);
```

**Constraints:**
- Unique product per cart (update quantity instead of duplicate)
- Quantity must be positive
- Price stored at time of addition

**Indexes:**
```sql
CREATE INDEX idx_cart_items_cart_id ON cart_items(cart_id);
CREATE INDEX idx_cart_items_product_id ON cart_items(product_id);
```

### 3.8 orders
Customer orders with address and payment information.

```sql
CREATE TABLE orders (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number text UNIQUE NOT NULL,
    user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE RESTRICT,
    status order_status DEFAULT 'pending',
    subtotal decimal(10,2) NOT NULL,
    shipping_cost decimal(10,2) NOT NULL DEFAULT 0,
    total decimal(10,2) NOT NULL,
    shipping_address jsonb NOT NULL,
    billing_address jsonb NOT NULL,
    payment_method text NOT NULL,
    notes text DEFAULT '',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);
```

**Constraints:**
- Order number must be unique
- Total = subtotal + shipping_cost
- Addresses stored as JSONB for flexibility

**Indexes:**
```sql
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_order_number ON orders(order_number);
CREATE INDEX idx_orders_created_at ON orders(created_at);
```

### 3.9 order_items
Products within orders with historical pricing.

```sql
CREATE TABLE order_items (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id uuid NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id uuid NOT NULL REFERENCES products(id) ON DELETE RESTRICT,
    quantity integer NOT NULL,
    price decimal(10,2) NOT NULL,
    product_name text NOT NULL,
    product_sku text NOT NULL,
    created_at timestamptz DEFAULT now()
);
```

**Constraints:**
- Historical product information stored
- Price at time of order
- Quantity must be positive

**Indexes:**
```sql
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);
```

## 4. Custom Types

### 4.1 Enums

```sql
-- User roles
CREATE TYPE user_role AS ENUM ('customer', 'admin');

-- Order statuses
CREATE TYPE order_status AS ENUM (
    'pending',
    'confirmed', 
    'processing',
    'shipped',
    'delivered',
    'cancelled'
);
```

## 5. Functions and Triggers

### 5.1 Updated At Trigger
Automatically updates the `updated_at` timestamp.

```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to relevant tables
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at
    BEFORE UPDATE ON categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ... (apply to other tables with updated_at)
```

### 5.2 Order Number Generation
Generates unique order numbers.

```sql
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS text AS $$
BEGIN
    RETURN 'ORD-' || LPAD(EXTRACT(epoch FROM now())::text, 10, '0');
END;
$$ LANGUAGE plpgsql;
```

## 6. Row Level Security (RLS) Policies

### 6.1 Profiles
```sql
-- Users can only access their own profile
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);
```

### 6.2 Products
```sql
-- Public read access for active products
CREATE POLICY "Anyone can view active products" ON products
    FOR SELECT USING (is_active = true);

-- Admin full access
CREATE POLICY "Admins can manage products" ON products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
```

### 6.3 Orders
```sql
-- Users can only access their own orders
CREATE POLICY "Users can view own orders" ON orders
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own orders" ON orders
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Admins can view all orders
CREATE POLICY "Admins can view all orders" ON orders
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
```

## 7. Data Validation

### 7.1 Check Constraints
```sql
-- Price validation
ALTER TABLE products ADD CONSTRAINT check_price_positive 
    CHECK (price > 0);

ALTER TABLE products ADD CONSTRAINT check_compare_price_higher 
    CHECK (compare_price IS NULL OR compare_price > price);

-- Quantity validation
ALTER TABLE cart_items ADD CONSTRAINT check_quantity_positive 
    CHECK (quantity > 0);

ALTER TABLE inventory ADD CONSTRAINT check_quantities_non_negative 
    CHECK (quantity >= 0 AND reserved_quantity >= 0);

-- Order total validation
ALTER TABLE orders ADD CONSTRAINT check_total_calculation 
    CHECK (total = subtotal + shipping_cost);
```

## 8. Performance Optimization

### 8.1 Composite Indexes
```sql
-- Product search optimization
CREATE INDEX idx_products_search ON products 
    USING gin(to_tsvector('english', name || ' ' || description));

-- Order filtering
CREATE INDEX idx_orders_user_status ON orders(user_id, status);
CREATE INDEX idx_orders_status_created ON orders(status, created_at);

-- Inventory alerts
CREATE INDEX idx_inventory_reorder_alert ON inventory(quantity, reorder_level) 
    WHERE quantity <= reorder_level;
```

### 8.2 Partial Indexes
```sql
-- Active products only
CREATE INDEX idx_products_active_only ON products(created_at) 
    WHERE is_active = true;

-- Featured products
CREATE INDEX idx_products_featured_only ON products(sort_order) 
    WHERE is_featured = true AND is_active = true;
```

## 9. Backup and Maintenance

### 9.1 Backup Strategy
- **Automated**: Supabase provides automated daily backups
- **Point-in-time**: Recovery available for the last 7 days
- **Manual**: Weekly manual backups for critical data

### 9.2 Maintenance Tasks
- **Weekly**: Analyze table statistics
- **Monthly**: Reindex heavily updated tables
- **Quarterly**: Review and optimize slow queries

---

**Document Version**: 1.0  
**Last Updated**: 2024-01-24  
**Next Review**: 2024-02-24
