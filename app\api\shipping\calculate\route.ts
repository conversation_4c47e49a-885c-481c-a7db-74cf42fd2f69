import { NextRequest, NextResponse } from 'next/server'
import { calculateShippingCost } from '@/lib/settings'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate the request body
    if (typeof body.subtotal !== 'number' || body.subtotal < 0) {
      return NextResponse.json(
        { error: 'Geçersiz ara toplam' },
        { status: 400 }
      )
    }

    const shippingCost = await calculateShippingCost(body.subtotal)
    
    return NextResponse.json({
      success: true,
      data: {
        subtotal: body.subtotal,
        shipping_cost: shippingCost,
        total: body.subtotal + shippingCost
      }
    })
  } catch (error) {
    console.error('Error calculating shipping cost:', error)
    return NextResponse.json(
      { error: 'Kargo ücreti hesaplanamadı' },
      { status: 500 }
    )
  }
}
