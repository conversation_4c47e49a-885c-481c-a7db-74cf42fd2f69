import { NextRequest, NextResponse } from 'next/server'
import { getOrderStats } from '@/lib/orders'
import { getInventoryStats } from '@/lib/inventory'

export async function GET(request: NextRequest) {
  try {
    // Get order stats
    const orderStats = await getOrderStats()

    // Get inventory stats
    const inventoryStats = await getInventoryStats()

    // Combine stats
    const stats = {
      total_orders: orderStats.total_orders,
      total_revenue: orderStats.total_revenue,
      pending_orders: orderStats.pending_orders,
      confirmed_orders: orderStats.confirmed_orders,
      shipped_orders: orderStats.shipped_orders,
      delivered_orders: orderStats.delivered_orders,
      cancelled_orders: orderStats.cancelled_orders,
      today_orders: orderStats.today_orders,
      this_month_revenue: orderStats.this_month_revenue,
      low_stock_products: inventoryStats.lowStock,
      out_of_stock_products: inventoryStats.outOfStock
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Admin stats API error:', error)
    return NextResponse.json(
      { error: '<PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON>nemedi' },
      { status: 500 }
    )
  }
}
