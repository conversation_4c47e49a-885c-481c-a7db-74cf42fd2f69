'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ProductCarousel } from '@/components/ui/product-carousel'
import { getProduct } from '@/lib/products'
import { addToCart } from '@/lib/cart'
import { getCurrentUser } from '@/lib/auth'
import { toast } from 'react-hot-toast'
import { 
  ShoppingCart, 
  Heart, 
  Share2, 
  Truck, 
  Shield, 
  ArrowLeft,
  Star,
  Plus,
  Minus
} from 'lucide-react'

export default function ProductDetailPage() {
  const params = useParams()
  const slug = params.slug as string
  
  const [product, setProduct] = useState<any>(null)
  const [selectedImage, setSelectedImage] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [isLoading, setIsLoading] = useState(true)
  const [isAddingToCart, setIsAddingToCart] = useState(false)
  const [isWishlisted, setIsWishlisted] = useState(false)

  useEffect(() => {
    if (slug) {
      loadProduct()
    }
  }, [slug])

  const loadProduct = async () => {
    try {
      const productData = await getProduct(slug)
      setProduct(productData)
    } catch (error) {
      console.error('Error loading product:', error)
      toast.error('Ürün bulunamadı')
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddToCart = async () => {
    if (!product) return

    setIsAddingToCart(true)
    try {
      const user = await getCurrentUser()
      if (!user) {
        toast.error('Sepete eklemek için giriş yapmalısınız')
        return
      }

      await addToCart(user.id, product.id, quantity)
      toast.success(`${quantity} adet ürün sepete eklendi`)
    } catch (error) {
      toast.error('Ürün sepete eklenemedi')
    } finally {
      setIsAddingToCart(false)
    }
  }

  const handleQuantityChange = (change: number) => {
    const newQuantity = quantity + change
    if (newQuantity >= 1 && newQuantity <= (product?.inventory?.quantity || 1)) {
      setQuantity(newQuantity)
    }
  }

  const handleWishlist = () => {
    setIsWishlisted(!isWishlisted)
    toast.success(isWishlisted ? 'Favorilerden çıkarıldı' : 'Favorilere eklendi')
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product?.name,
          text: product?.short_description,
          url: window.location.href,
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      toast.success('Link kopyalandı')
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-4">
              <div className="aspect-square bg-gray-200 rounded-lg"></div>
              <div className="grid grid-cols-4 gap-2">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="aspect-square bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
            <div className="space-y-4">
              <div className="h-8 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-6 bg-gray-200 rounded w-1/2"></div>
              <div className="h-20 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Ürün Bulunamadı</h1>
        <p className="text-gray-600 mb-8">Aradığınız ürün mevcut değil veya kaldırılmış olabilir.</p>
        <Button asChild>
          <Link href="/products">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Ürünlere Dön
          </Link>
        </Button>
      </div>
    )
  }

  const isInStock = product.inventory && product.inventory.quantity > 0
  const images = product.images || []
  const mainImage = images[selectedImage] || images[0]

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
        <Link href="/" className="hover:text-gray-900">Ana Sayfa</Link>
        <span>/</span>
        <Link href="/products" className="hover:text-gray-900">Ürünler</Link>
        {product.category && (
          <>
            <span>/</span>
            <Link href={`/category/${product.category.slug}`} className="hover:text-gray-900">
              {product.category.name}
            </Link>
          </>
        )}
        <span>/</span>
        <span className="text-gray-900">{product.name}</span>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Product Images */}
        <div className="space-y-4">
          <ProductCarousel
            images={images}
            productName={product.name}
            showThumbnails={true}
            autoPlay={false}
          />
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          {/* Title and Category */}
          <div>
            {product.category && (
              <Link 
                href={`/category/${product.category.slug}`}
                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
              >
                {product.category.name}
              </Link>
            )}
            <h1 className="text-3xl font-bold text-gray-900 mt-2">{product.name}</h1>
            {product.short_description && (
              <p className="text-gray-600 mt-2">{product.short_description}</p>
            )}
          </div>

          {/* Price */}
          <div className="flex items-center space-x-4">
            <span className="text-3xl font-bold text-gray-900">
              {product.price.toLocaleString('tr-TR')} ₺
            </span>
            {product.compare_price && product.compare_price > product.price && (
              <span className="text-xl text-gray-500 line-through">
                {product.compare_price.toLocaleString('tr-TR')} ₺
              </span>
            )}
          </div>

          {/* Stock Status */}
          <div className="flex items-center space-x-2">
            {isInStock ? (
              <>
                <Badge variant="outline" className="text-green-600 border-green-600">
                  Stokta Var
                </Badge>
                <span className="text-sm text-gray-600">
                  {product.inventory.quantity} adet mevcut
                </span>
              </>
            ) : (
              <Badge variant="outline" className="text-red-600 border-red-600">
                Stokta Yok
              </Badge>
            )}
          </div>

          {/* Quantity and Add to Cart */}
          {isInStock && (
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium">Miktar:</span>
                <div className="flex items-center border rounded-lg">
                  <button
                    onClick={() => handleQuantityChange(-1)}
                    disabled={quantity <= 1}
                    className="p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                  <span className="px-4 py-2 min-w-[3rem] text-center">{quantity}</span>
                  <button
                    onClick={() => handleQuantityChange(1)}
                    disabled={quantity >= (product.inventory?.quantity || 1)}
                    className="p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <div className="flex space-x-4">
                <Button
                  onClick={handleAddToCart}
                  disabled={isAddingToCart}
                  className="flex-1"
                  size="lg"
                >
                  <ShoppingCart className="mr-2 h-5 w-5" />
                  {isAddingToCart ? 'Ekleniyor...' : 'Sepete Ekle'}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleWishlist}
                  size="lg"
                  className={isWishlisted ? 'text-red-600 border-red-600' : ''}
                >
                  <Heart className={`h-5 w-5 ${isWishlisted ? 'fill-current' : ''}`} />
                </Button>
                <Button variant="outline" onClick={handleShare} size="lg">
                  <Share2 className="h-5 w-5" />
                </Button>
              </div>
            </div>
          )}

          {/* Features */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Truck className="h-4 w-4" />
              <span>Ücretsiz Kargo</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Shield className="h-4 w-4" />
              <span>Güvenli Ödeme</span>
            </div>
          </div>

          <Separator />

          {/* Description */}
          {product.description && (
            <div>
              <h3 className="text-lg font-semibold mb-3">Ürün Açıklaması</h3>
              <div 
                className="prose prose-sm max-w-none text-gray-700"
                dangerouslySetInnerHTML={{ __html: product.description }}
              />
            </div>
          )}

          {/* Product Details */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Ürün Detayları</h3>
            <dl className="grid grid-cols-1 gap-2 text-sm">
              <div className="flex justify-between">
                <dt className="text-gray-600">SKU:</dt>
                <dd className="font-medium">{product.sku}</dd>
              </div>
              {product.material_type && (
                <div className="flex justify-between">
                  <dt className="text-gray-600">Malzeme:</dt>
                  <dd className="font-medium">{product.material_type}</dd>
                </div>
              )}
              {product.dimensions && (
                <div className="flex justify-between">
                  <dt className="text-gray-600">Boyutlar:</dt>
                  <dd className="font-medium">{product.dimensions}</dd>
                </div>
              )}
              {product.weight_grams > 0 && (
                <div className="flex justify-between">
                  <dt className="text-gray-600">Ağırlık:</dt>
                  <dd className="font-medium">{product.weight_grams}g</dd>
                </div>
              )}
            </dl>
          </div>
        </div>
      </div>
    </div>
  )
}
