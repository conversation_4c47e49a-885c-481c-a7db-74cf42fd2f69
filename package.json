{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@next/swc-wasm-nodejs": "13.5.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.52.1", "@types/node": "^24.1.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "10.4.15", "caniuse-lite": "^1.0.30001727", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.3.0", "eslint": "8.49.0", "eslint-config-next": "15.4.3", "framer-motion": "^12.23.9", "input-otp": "^1.2.4", "lucide-react": "^0.525.0", "next": "^15.4.3", "next-themes": "^0.3.0", "postcss": "8.4.30", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "react-resizable-panels": "^2.1.3", "recharts": "^3.1.0", "sonner": "^1.5.0", "tailwind-merge": "^3.3.1", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "vaul": "^0.9.9", "zod": "^4.0.8"}, "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"typescript": "^5"}}