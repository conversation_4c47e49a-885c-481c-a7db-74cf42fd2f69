@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 20 14.3% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 20 14.3% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 20 14.3% 4.1%;
    --primary: 24 9.8% 10%;
    --primary-foreground: 60 9.1% 97.8%;
    --secondary: 60 4.8% 95.9%;
    --secondary-foreground: 24 9.8% 10%;
    --muted: 60 4.8% 95.9%;
    --muted-foreground: 25 5.3% 44.7%;
    --accent: 60 4.8% 95.9%;
    --accent-foreground: 24 9.8% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 60 9.1% 97.8%;
    --border: 20 5.9% 90%;
    --input: 20 5.9% 90%;
    --ring: 20 14.3% 4.1%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    /* Brand colors */
    --brand: 210 100% 50%;
    --brand-foreground: 0 0% 100%;
    --brand-secondary: 210 80% 60%;
    --brand-muted: 210 30% 90%;

    /* Status colors */
    --success: 120 60% 50%;
    --success-foreground: 0 0% 100%;
    --warning: 45 100% 60%;
    --warning-foreground: 0 0% 0%;
    --info: 210 100% 60%;
    --info-foreground: 0 0% 100%;

    /* Gradient colors */
    --gradient-from: 45 100% 70%;
    --gradient-via: 30 100% 60%;
    --gradient-to: 0 100% 60%;
    --gradient-brand-from: 270 70% 60%;
    --gradient-brand-via: 210 100% 60%;
    --gradient-brand-to: 260 100% 70%;

    /* Surface colors */
    --surface: 0 0% 98%;
    --surface-foreground: 20 14.3% 4.1%;
    --overlay: 0 0% 0%;
    --overlay-foreground: 0 0% 100%;
  }
  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 60 9.1% 97.8%;
    --card: 20 14.3% 4.1%;
    --card-foreground: 60 9.1% 97.8%;
    --popover: 20 14.3% 4.1%;
    --popover-foreground: 60 9.1% 97.8%;
    --primary: 60 9.1% 97.8%;
    --primary-foreground: 24 9.8% 10%;
    --secondary: 12 6.5% 15.1%;
    --secondary-foreground: 60 9.1% 97.8%;
    --muted: 12 6.5% 15.1%;
    --muted-foreground: 24 5.4% 63.9%;
    --accent: 12 6.5% 15.1%;
    --accent-foreground: 60 9.1% 97.8%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 60 9.1% 97.8%;
    --border: 12 6.5% 15.1%;
    --input: 12 6.5% 15.1%;
    --ring: 24 5.7% 82.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Brand colors */
    --brand: 210 100% 60%;
    --brand-foreground: 20 14.3% 4.1%;
    --brand-secondary: 210 80% 70%;
    --brand-muted: 210 20% 20%;

    /* Status colors */
    --success: 120 60% 60%;
    --success-foreground: 20 14.3% 4.1%;
    --warning: 45 100% 70%;
    --warning-foreground: 20 14.3% 4.1%;
    --info: 210 100% 70%;
    --info-foreground: 20 14.3% 4.1%;

    /* Gradient colors */
    --gradient-from: 45 100% 80%;
    --gradient-via: 30 100% 70%;
    --gradient-to: 0 100% 70%;
    --gradient-brand-from: 270 70% 70%;
    --gradient-brand-via: 210 100% 70%;
    --gradient-brand-to: 260 100% 80%;

    /* Surface colors */
    --surface: 12 6.5% 15.1%;
    --surface-foreground: 60 9.1% 97.8%;
    --overlay: 0 0% 0%;
    --overlay-foreground: 0 0% 100%;
  }

  .blue-ocean {
    --background: 210 40% 98%;
    --foreground: 210 40% 8%;
    --card: 210 40% 98%;
    --card-foreground: 210 40% 8%;
    --popover: 210 40% 98%;
    --popover-foreground: 210 40% 8%;
    --primary: 210 100% 50%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 30% 90%;
    --secondary-foreground: 210 40% 8%;
    --muted: 210 30% 90%;
    --muted-foreground: 210 20% 40%;
    --accent: 210 30% 90%;
    --accent-foreground: 210 40% 8%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 60 9.1% 97.8%;
    --border: 210 30% 85%;
    --input: 210 30% 85%;
    --ring: 210 100% 50%;
    --chart-1: 210 100% 60%;
    --chart-2: 190 100% 50%;
    --chart-3: 230 100% 60%;
    --chart-4: 200 100% 55%;
    --chart-5: 220 100% 65%;

    /* Brand colors */
    --brand: 210 100% 50%;
    --brand-foreground: 210 40% 98%;
    --brand-secondary: 210 80% 60%;
    --brand-muted: 210 30% 90%;

    /* Status colors */
    --success: 150 60% 50%;
    --success-foreground: 210 40% 98%;
    --warning: 45 100% 60%;
    --warning-foreground: 210 40% 8%;
    --info: 210 100% 60%;
    --info-foreground: 210 40% 98%;

    /* Gradient colors */
    --gradient-from: 200 100% 70%;
    --gradient-via: 210 100% 60%;
    --gradient-to: 220 100% 70%;
    --gradient-brand-from: 190 100% 60%;
    --gradient-brand-via: 210 100% 50%;
    --gradient-brand-to: 230 100% 60%;

    /* Surface colors */
    --surface: 210 40% 96%;
    --surface-foreground: 210 40% 8%;
    --overlay: 210 40% 8%;
    --overlay-foreground: 210 40% 98%;
  }

  .green-forest {
    --background: 120 40% 98%;
    --foreground: 120 40% 8%;
    --card: 120 40% 98%;
    --card-foreground: 120 40% 8%;
    --popover: 120 40% 98%;
    --popover-foreground: 120 40% 8%;
    --primary: 120 60% 40%;
    --primary-foreground: 120 40% 98%;
    --secondary: 120 30% 90%;
    --secondary-foreground: 120 40% 8%;
    --muted: 120 30% 90%;
    --muted-foreground: 120 20% 40%;
    --accent: 120 30% 90%;
    --accent-foreground: 120 40% 8%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 60 9.1% 97.8%;
    --border: 120 30% 85%;
    --input: 120 30% 85%;
    --ring: 120 60% 40%;
    --chart-1: 120 60% 50%;
    --chart-2: 100 60% 45%;
    --chart-3: 140 60% 45%;
    --chart-4: 110 60% 50%;
    --chart-5: 130 60% 50%;

    /* Brand colors */
    --brand: 120 60% 40%;
    --brand-foreground: 120 40% 98%;
    --brand-secondary: 120 50% 50%;
    --brand-muted: 120 30% 90%;

    /* Status colors */
    --success: 120 60% 50%;
    --success-foreground: 120 40% 98%;
    --warning: 45 100% 60%;
    --warning-foreground: 120 40% 8%;
    --info: 200 100% 60%;
    --info-foreground: 120 40% 98%;

    /* Gradient colors */
    --gradient-from: 100 60% 60%;
    --gradient-via: 120 60% 50%;
    --gradient-to: 140 60% 60%;
    --gradient-brand-from: 110 60% 50%;
    --gradient-brand-via: 120 60% 40%;
    --gradient-brand-to: 130 60% 50%;

    /* Surface colors */
    --surface: 120 40% 96%;
    --surface-foreground: 120 40% 8%;
    --overlay: 120 40% 8%;
    --overlay-foreground: 120 40% 98%;
  }

  .purple-galaxy {
    --background: 270 40% 98%;
    --foreground: 270 40% 8%;
    --card: 270 40% 98%;
    --card-foreground: 270 40% 8%;
    --popover: 270 40% 98%;
    --popover-foreground: 270 40% 8%;
    --primary: 270 70% 50%;
    --primary-foreground: 270 40% 98%;
    --secondary: 270 30% 90%;
    --secondary-foreground: 270 40% 8%;
    --muted: 270 30% 90%;
    --muted-foreground: 270 20% 40%;
    --accent: 270 30% 90%;
    --accent-foreground: 270 40% 8%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 60 9.1% 97.8%;
    --border: 270 30% 85%;
    --input: 270 30% 85%;
    --ring: 270 70% 50%;
    --chart-1: 270 70% 60%;
    --chart-2: 250 70% 55%;
    --chart-3: 290 70% 55%;
    --chart-4: 260 70% 60%;
    --chart-5: 280 70% 60%;

    /* Brand colors */
    --brand: 270 70% 50%;
    --brand-foreground: 270 40% 98%;
    --brand-secondary: 270 60% 60%;
    --brand-muted: 270 30% 90%;

    /* Status colors */
    --success: 120 60% 50%;
    --success-foreground: 270 40% 98%;
    --warning: 45 100% 60%;
    --warning-foreground: 270 40% 8%;
    --info: 210 100% 60%;
    --info-foreground: 270 40% 98%;

    /* Gradient colors */
    --gradient-from: 250 70% 60%;
    --gradient-via: 270 70% 50%;
    --gradient-to: 290 70% 60%;
    --gradient-brand-from: 260 70% 60%;
    --gradient-brand-via: 270 70% 50%;
    --gradient-brand-to: 280 70% 60%;

    /* Surface colors */
    --surface: 270 40% 96%;
    --surface-foreground: 270 40% 8%;
    --overlay: 270 40% 8%;
    --overlay-foreground: 270 40% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
