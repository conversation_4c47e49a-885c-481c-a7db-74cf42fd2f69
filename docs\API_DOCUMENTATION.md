# API Documentation

## 1. Overview

The 3Dünyam API provides RESTful endpoints for managing an e-commerce platform specialized in 3D printed products. All endpoints return JSON responses and follow standard HTTP status codes.

### Base URL
```
Production: https://3dunyam.vercel.app/api
Development: http://localhost:3000/api
```

### Authentication
The API uses JWT tokens for authentication. Include the token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

### Response Format
All API responses follow this standard format:

#### Success Response
```json
{
  "success": true,
  "data": <response_data>,
  "message": "Optional success message",
  "meta": {
    "total": 100,
    "page": 1,
    "limit": 20
  }
}
```

#### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": "Additional error details"
  }
}
```

## 2. Public Endpoints

### 2.1 Products

#### GET /api/products
Retrieve a list of products with optional filtering and pagination.

**Parameters:**
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 20, max: 100)
- `category` (string, optional): Filter by category slug
- `search` (string, optional): Search in product name and description
- `min_price` (number, optional): Minimum price filter
- `max_price` (number, optional): Maximum price filter
- `material` (string, optional): Filter by material type
- `sort` (string, optional): Sort order (price_asc, price_desc, name_asc, name_desc, created_desc)

**Example Request:**
```bash
GET /api/products?page=1&limit=20&category=decorative&sort=price_asc
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "Decorative Moon Lamp",
      "slug": "decorative-moon-lamp",
      "short_description": "Beautiful 3D printed moon lamp",
      "price": 299.99,
      "compare_price": 399.99,
      "sku": "MOON-001",
      "material_type": "PLA+",
      "dimensions": "15x15x15 cm",
      "weight_grams": 200,
      "is_active": true,
      "is_featured": true,
      "category": {
        "id": "uuid",
        "name": "Decorative Items",
        "slug": "decorative"
      },
      "images": [
        {
          "id": "uuid",
          "image_url": "https://example.com/image.jpg",
          "alt_text": "Moon lamp front view",
          "is_primary": true
        }
      ],
      "inventory": {
        "quantity": 50,
        "reserved_quantity": 5
      }
    }
  ],
  "meta": {
    "total": 150,
    "page": 1,
    "limit": 20,
    "total_pages": 8
  }
}
```

#### GET /api/products/[slug]
Retrieve detailed information about a specific product.

**Parameters:**
- `slug` (string): Product slug

**Example Request:**
```bash
GET /api/products/decorative-moon-lamp
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Decorative Moon Lamp",
    "slug": "decorative-moon-lamp",
    "description": "Detailed product description...",
    "short_description": "Beautiful 3D printed moon lamp",
    "price": 299.99,
    "compare_price": 399.99,
    "sku": "MOON-001",
    "material_type": "PLA+",
    "dimensions": "15x15x15 cm",
    "weight_grams": 200,
    "is_active": true,
    "is_featured": true,
    "seo_title": "Decorative Moon Lamp - 3D Printed",
    "seo_description": "SEO description...",
    "category": {
      "id": "uuid",
      "name": "Decorative Items",
      "slug": "decorative"
    },
    "images": [
      {
        "id": "uuid",
        "image_url": "https://example.com/image1.jpg",
        "alt_text": "Moon lamp front view",
        "sort_order": 0,
        "is_primary": true
      }
    ],
    "inventory": {
      "quantity": 50,
      "reserved_quantity": 5,
      "reorder_level": 10
    },
    "related_products": [
      {
        "id": "uuid",
        "name": "Star Lamp",
        "slug": "star-lamp",
        "price": 199.99,
        "image_url": "https://example.com/star.jpg"
      }
    ]
  }
}
```

### 2.2 Categories

#### GET /api/categories
Retrieve all product categories.

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "Decorative Items",
      "slug": "decorative",
      "description": "Beautiful decorative 3D printed items",
      "image_url": "https://example.com/category.jpg",
      "is_active": true,
      "sort_order": 1,
      "product_count": 25
    }
  ]
}
```

## 3. Authenticated Endpoints

### 3.1 Cart Management

#### GET /api/cart
Retrieve the current user's cart.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "user_id": "uuid",
    "items": [
      {
        "id": "uuid",
        "product_id": "uuid",
        "quantity": 2,
        "price": 299.99,
        "product": {
          "id": "uuid",
          "name": "Moon Lamp",
          "slug": "moon-lamp",
          "sku": "MOON-001",
          "image_url": "https://example.com/image.jpg"
        }
      }
    ],
    "subtotal": 599.98,
    "total_items": 2
  }
}
```

#### POST /api/cart
Add an item to the cart.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "product_id": "uuid",
  "quantity": 1
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "cart_id": "uuid",
    "product_id": "uuid",
    "quantity": 1,
    "price": 299.99
  },
  "message": "Item added to cart"
}
```

#### PUT /api/cart/[item_id]
Update cart item quantity.

**Request Body:**
```json
{
  "quantity": 3
}
```

#### DELETE /api/cart/[item_id]
Remove item from cart.

**Example Response:**
```json
{
  "success": true,
  "message": "Item removed from cart"
}
```

### 3.2 Orders

#### GET /api/orders
Retrieve user's order history.

**Parameters:**
- `page` (number, optional): Page number
- `limit` (number, optional): Items per page
- `status` (string, optional): Filter by order status

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "order_number": "ORD-12345678",
      "status": "delivered",
      "subtotal": 599.98,
      "shipping_cost": 29.99,
      "total": 629.97,
      "created_at": "2024-01-24T10:00:00Z",
      "items": [
        {
          "id": "uuid",
          "product_name": "Moon Lamp",
          "product_sku": "MOON-001",
          "quantity": 2,
          "price": 299.99
        }
      ]
    }
  ],
  "meta": {
    "total": 5,
    "page": 1,
    "limit": 20
  }
}
```

#### POST /api/orders
Create a new order.

**Request Body:**
```json
{
  "shipping_address": {
    "first_name": "John",
    "last_name": "Doe",
    "address_line1": "123 Main St",
    "city": "Istanbul",
    "postal_code": "34000",
    "country": "Turkey",
    "phone": "+90 ************"
  },
  "billing_address": {
    "first_name": "John",
    "last_name": "Doe",
    "address_line1": "123 Main St",
    "city": "Istanbul",
    "postal_code": "34000",
    "country": "Turkey"
  },
  "payment_method": "credit-card",
  "notes": "Please handle with care"
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "order_number": "ORD-12345678",
    "status": "pending",
    "total": 629.97,
    "created_at": "2024-01-24T10:00:00Z"
  },
  "message": "Order created successfully"
}
```

### 3.3 User Profile

#### GET /api/profile
Get current user's profile.

**Example Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+90 ************",
    "role": "customer",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### PUT /api/profile
Update user profile.

**Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+90 ************"
}
```

## 4. Admin Endpoints

All admin endpoints require authentication and admin role.

### 4.1 Product Management

#### GET /api/admin/products
List all products (admin view).

**Parameters:**
- `page`, `limit`, `search`, `category`, `status`

#### POST /api/admin/products
Create a new product.

**Request Body:**
```json
{
  "name": "New Product",
  "slug": "new-product",
  "description": "Product description",
  "short_description": "Short description",
  "price": 199.99,
  "compare_price": 299.99,
  "sku": "NEW-001",
  "category_id": "uuid",
  "material_type": "PLA+",
  "dimensions": "10x10x10 cm",
  "weight_grams": 150,
  "is_active": true,
  "is_featured": false,
  "images": [
    {
      "image_url": "https://example.com/image.jpg",
      "alt_text": "Product image",
      "sort_order": 0,
      "is_primary": true
    }
  ],
  "inventory": {
    "quantity": 100,
    "reorder_level": 10
  }
}
```

#### PUT /api/admin/products/[id]
Update an existing product.

#### DELETE /api/admin/products/[id]
Delete a product.

### 4.2 Order Management

#### GET /api/admin/orders
List all orders.

#### PUT /api/admin/orders/[id]
Update order status.

**Request Body:**
```json
{
  "status": "shipped",
  "tracking_number": "TRK123456789"
}
```

### 4.3 Inventory Management

#### PUT /api/admin/inventory/[product_id]
Update product inventory.

**Request Body:**
```json
{
  "quantity": 50,
  "reserved_quantity": 5,
  "reorder_level": 10
}
```

### 4.4 Dashboard Stats

#### GET /api/admin/stats
Get dashboard statistics.

**Example Response:**
```json
{
  "success": true,
  "data": {
    "total_orders": 150,
    "total_revenue": 45000.00,
    "pending_orders": 12,
    "low_stock_products": 5,
    "total_products": 85,
    "active_users": 250
  }
}
```

## 5. Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `UNAUTHORIZED` | Authentication required |
| `FORBIDDEN` | Insufficient permissions |
| `NOT_FOUND` | Resource not found |
| `CONFLICT` | Resource conflict (e.g., duplicate SKU) |
| `INSUFFICIENT_STOCK` | Not enough inventory |
| `PAYMENT_FAILED` | Payment processing failed |
| `INTERNAL_ERROR` | Server error |

## 6. Rate Limiting

- **Public endpoints**: 100 requests per minute per IP
- **Authenticated endpoints**: 1000 requests per minute per user
- **Admin endpoints**: 2000 requests per minute per admin

## 7. Webhooks

### Order Status Updates
```
POST /api/webhooks/order-status
```

Triggered when order status changes.

**Payload:**
```json
{
  "event": "order.status_changed",
  "data": {
    "order_id": "uuid",
    "order_number": "ORD-12345678",
    "old_status": "pending",
    "new_status": "confirmed",
    "timestamp": "2024-01-24T10:00:00Z"
  }
}
```

---

**Document Version**: 1.0  
**Last Updated**: 2024-01-24  
**Next Review**: 2024-02-24
