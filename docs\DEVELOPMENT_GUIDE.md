# Development Guide

## 1. Getting Started

### 1.1 Prerequisites
- **Node.js**: v18.0.0 or higher
- **Bun**: Latest version (package manager)
- **Git**: For version control
- **VS Code**: Recommended IDE with extensions

### 1.2 Required VS Code Extensions
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode.vscode-eslint",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

### 1.3 Environment Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd 3dunyam-platform
```

2. **Install dependencies**
```bash
bun install
```

3. **Environment variables**
```bash
cp .env.example .env
```

Required environment variables:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Optional
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

4. **Start development server**
```bash
bun dev
```

## 2. Project Structure

```
3dunyam-platform/
├── app/                          # Next.js 14 App Router
│   ├── (auth)/                   # Auth route group
│   │   ├── login/
│   │   └── register/
│   ├── admin/                    # Admin route group
│   │   ├── dashboard/
│   │   ├── products/
│   │   ├── orders/
│   │   └── inventory/
│   ├── api/                      # API routes
│   │   ├── auth/
│   │   ├── products/
│   │   ├── orders/
│   │   └── admin/
│   ├── products/                 # Product pages
│   ├── cart/                     # Shopping cart
│   ├── checkout/                 # Checkout process
│   ├── profile/                  # User profile
│   ├── globals.css               # Global styles
│   ├── layout.tsx                # Root layout
│   └── page.tsx                  # Home page
├── components/                   # Reusable components
│   ├── ui/                       # Shadcn/ui components
│   ├── layout/                   # Layout components
│   ├── forms/                    # Form components
│   └── product/                  # Product-specific components
├── lib/                          # Utility libraries
│   ├── auth.ts                   # Authentication utilities
│   ├── supabase.ts               # Supabase client
│   ├── products.ts               # Product operations
│   ├── orders.ts                 # Order operations
│   ├── inventory.ts              # Inventory operations
│   ├── utils.ts                  # General utilities
│   └── validations.ts            # Zod schemas
├── hooks/                        # Custom React hooks
├── types/                        # TypeScript type definitions
├── docs/                         # Documentation
├── supabase/                     # Database migrations
│   └── migrations/
├── public/                       # Static assets
├── .env                          # Environment variables
├── next.config.js                # Next.js configuration
├── tailwind.config.js            # Tailwind CSS configuration
├── tsconfig.json                 # TypeScript configuration
└── package.json                  # Dependencies
```

## 3. Development Workflow

### 3.1 Git Workflow
We follow **Git Flow** with the following branches:

- `main`: Production-ready code
- `develop`: Integration branch for features
- `feature/*`: Feature development
- `hotfix/*`: Critical bug fixes
- `release/*`: Release preparation

**Branch naming convention:**
```
feature/user-authentication
feature/product-catalog
bugfix/cart-calculation
hotfix/security-patch
```

### 3.2 Commit Convention
We use **Conventional Commits** format:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(auth): add user registration
fix(cart): resolve quantity calculation bug
docs(api): update endpoint documentation
```

### 3.3 Pull Request Process

1. **Create feature branch**
```bash
git checkout -b feature/new-feature
```

2. **Make changes and commit**
```bash
git add .
git commit -m "feat(scope): description"
```

3. **Push and create PR**
```bash
git push origin feature/new-feature
```

4. **PR Requirements:**
   - Descriptive title and description
   - Link to related issues
   - Screenshots for UI changes
   - Tests passing
   - Code review approval

## 4. Coding Standards

### 4.1 TypeScript Guidelines

**Use strict typing:**
```typescript
// ✅ Good
interface User {
  id: string
  email: string
  name: string
}

// ❌ Avoid
const user: any = getData()
```

**Prefer interfaces over types for objects:**
```typescript
// ✅ Good
interface ProductProps {
  product: Product
  onAddToCart: (id: string) => void
}

// ✅ Also good for unions
type Status = 'pending' | 'confirmed' | 'shipped'
```

### 4.2 React Component Guidelines

**Use functional components with hooks:**
```typescript
// ✅ Good
const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const [isLoading, setIsLoading] = useState(false)
  
  return (
    <div className="product-card">
      {/* component content */}
    </div>
  )
}
```

**Component file structure:**
```typescript
// 1. Imports
import React from 'react'
import { Button } from '@/components/ui/button'

// 2. Types/Interfaces
interface ComponentProps {
  // props definition
}

// 3. Component
const Component: React.FC<ComponentProps> = ({ prop }) => {
  // 4. Hooks
  const [state, setState] = useState()
  
  // 5. Functions
  const handleClick = () => {
    // logic
  }
  
  // 6. Effects
  useEffect(() => {
    // side effects
  }, [])
  
  // 7. Render
  return (
    <div>
      {/* JSX */}
    </div>
  )
}

// 8. Export
export default Component
```

### 4.3 CSS/Tailwind Guidelines

**Use Tailwind utility classes:**
```tsx
// ✅ Good
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-md">
  <h2 className="text-xl font-semibold text-gray-900">Title</h2>
  <Button variant="primary" size="sm">Action</Button>
</div>
```

**Create custom components for repeated patterns:**
```tsx
// components/ui/card.tsx
const Card = ({ children, className, ...props }) => (
  <div 
    className={cn("bg-white rounded-lg shadow-md p-6", className)}
    {...props}
  >
    {children}
  </div>
)
```

### 4.4 API Route Guidelines

**Consistent error handling:**
```typescript
// app/api/products/route.ts
export async function GET(request: NextRequest) {
  try {
    const products = await getProducts()
    
    return NextResponse.json({
      success: true,
      data: products
    })
  } catch (error) {
    console.error('Products API error:', error)
    
    return NextResponse.json(
      { 
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch products'
        }
      },
      { status: 500 }
    )
  }
}
```

## 5. Testing Strategy

### 5.1 Testing Pyramid
```
    /\
   /  \     E2E Tests (Few)
  /____\    
 /      \   Integration Tests (Some)
/________\  Unit Tests (Many)
```

### 5.2 Unit Testing
```typescript
// __tests__/utils/formatPrice.test.ts
import { formatPrice } from '@/lib/utils'

describe('formatPrice', () => {
  it('should format price correctly', () => {
    expect(formatPrice(1234.56)).toBe('1.234,56 ₺')
    expect(formatPrice(0)).toBe('0,00 ₺')
  })
})
```

### 5.3 Component Testing
```typescript
// __tests__/components/ProductCard.test.tsx
import { render, screen } from '@testing-library/react'
import ProductCard from '@/components/ProductCard'

const mockProduct = {
  id: '1',
  name: 'Test Product',
  price: 99.99
}

describe('ProductCard', () => {
  it('renders product information', () => {
    render(<ProductCard product={mockProduct} />)
    
    expect(screen.getByText('Test Product')).toBeInTheDocument()
    expect(screen.getByText('99,99 ₺')).toBeInTheDocument()
  })
})
```

### 5.4 Running Tests
```bash
# Run all tests
bun test

# Run tests in watch mode
bun test --watch

# Run tests with coverage
bun test --coverage

# Run specific test file
bun test ProductCard.test.tsx
```

## 6. Performance Guidelines

### 6.1 Next.js Optimization

**Use appropriate rendering methods:**
```typescript
// Static generation for product pages
export async function generateStaticParams() {
  const products = await getProducts()
  return products.map((product) => ({
    slug: product.slug,
  }))
}

// Server components for data fetching
async function ProductList() {
  const products = await getProducts()
  return (
    <div>
      {products.map(product => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  )
}
```

**Image optimization:**
```tsx
import Image from 'next/image'

<Image
  src={product.imageUrl}
  alt={product.name}
  width={300}
  height={300}
  className="object-cover"
  priority={isAboveFold}
/>
```

### 6.2 Database Optimization

**Use proper indexing:**
```sql
-- Add indexes for frequently queried columns
CREATE INDEX idx_products_category_active ON products(category_id, is_active);
CREATE INDEX idx_orders_user_status ON orders(user_id, status);
```

**Optimize queries:**
```typescript
// ✅ Good - Select only needed fields
const products = await supabase
  .from('products')
  .select('id, name, price, image_url')
  .eq('is_active', true)

// ❌ Avoid - Select all fields
const products = await supabase
  .from('products')
  .select('*')
```

## 7. Security Guidelines

### 7.1 Authentication
```typescript
// Always verify user authentication
const user = await getCurrentUser()
if (!user) {
  return NextResponse.json(
    { error: 'Unauthorized' },
    { status: 401 }
  )
}
```

### 7.2 Input Validation
```typescript
import { z } from 'zod'

const createProductSchema = z.object({
  name: z.string().min(1).max(255),
  price: z.number().positive(),
  sku: z.string().min(1)
})

// Validate input
const result = createProductSchema.safeParse(requestData)
if (!result.success) {
  return NextResponse.json(
    { error: 'Validation failed', details: result.error },
    { status: 400 }
  )
}
```

### 7.3 SQL Injection Prevention
```typescript
// ✅ Good - Use Supabase client (parameterized)
const products = await supabase
  .from('products')
  .select('*')
  .eq('category_id', categoryId)

// ❌ Never - Raw SQL with user input
const query = `SELECT * FROM products WHERE category_id = '${categoryId}'`
```

## 8. Deployment

### 8.1 Environment Configuration
```typescript
// lib/config.ts
export const config = {
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    serviceKey: process.env.SUPABASE_SERVICE_ROLE_KEY!
  },
  app: {
    url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
  }
}
```

### 8.2 Build Process
```bash
# Build for production
bun run build

# Start production server
bun start

# Lint code
bun run lint

# Type check
bun run type-check
```

### 8.3 Deployment Checklist
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Tests passing
- [ ] Build successful
- [ ] Performance audit passed
- [ ] Security scan completed

## 9. Troubleshooting

### 9.1 Common Issues

**Supabase connection errors:**
```typescript
// Check environment variables
console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)

// Verify RLS policies
const { data, error } = await supabase
  .from('products')
  .select('*')
  
if (error) {
  console.error('Supabase error:', error)
}
```

**Build errors:**
```bash
# Clear Next.js cache
rm -rf .next

# Reinstall dependencies
rm -rf node_modules
bun install

# Check TypeScript errors
bun run type-check
```

### 9.2 Debug Mode
```typescript
// Enable debug logging
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info:', { user, products, error })
}
```

---

**Document Version**: 1.0  
**Last Updated**: 2024-01-24  
**Next Review**: 2024-02-24
