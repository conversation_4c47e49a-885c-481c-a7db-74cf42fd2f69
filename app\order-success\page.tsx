'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { CheckCircle, Package, Truck, ArrowRight, Home } from 'lucide-react'

export default function OrderSuccessPage() {
  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-2xl mx-auto text-center">
        {/* Success Icon */}
        <div className="mb-8">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="h-12 w-12 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Siparişiniz Alındı!
          </h1>
          <p className="text-gray-600 text-lg">
            Teşekkür ederiz! Sipariş<PERSON>z başar<PERSON><PERSON> i<PERSON>lem<PERSON> alındı.
          </p>
        </div>

        {/* Order Details */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Sipariş Detayları</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Sipariş Numarası:</span>
                <p className="font-medium">#ORD-{Date.now().toString().slice(-6)}</p>
              </div>
              <div>
                <span className="text-gray-600">Sipariş Tarihi:</span>
                <p className="font-medium">{new Date().toLocaleDateString('tr-TR')}</p>
              </div>
              <div>
                <span className="text-gray-600">Ödeme Durumu:</span>
                <p className="font-medium text-green-600">Onaylandı</p>
              </div>
              <div>
                <span className="text-gray-600">Teslimat Durumu:</span>
                <p className="font-medium text-blue-600">Hazırlanıyor</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Sonraki Adımlar</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-blue-600 font-semibold text-sm">1</span>
                </div>
                <div className="text-left">
                  <h3 className="font-medium">Sipariş Onayı</h3>
                  <p className="text-sm text-gray-600">
                    E-posta adresinize sipariş onay maili gönderildi.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Package className="h-4 w-4 text-yellow-600" />
                </div>
                <div className="text-left">
                  <h3 className="font-medium">Hazırlık Süreci</h3>
                  <p className="text-sm text-gray-600">
                    Siparişiniz 1-2 iş günü içinde hazırlanacak.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Truck className="h-4 w-4 text-green-600" />
                </div>
                <div className="text-left">
                  <h3 className="font-medium">Kargo & Teslimat</h3>
                  <p className="text-sm text-gray-600">
                    Kargo takip numarası e-posta ile gönderilecek.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/products">
                Alışverişe Devam Et
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            
            <Button asChild variant="outline" size="lg">
              <Link href="/">
                <Home className="mr-2 h-5 w-5" />
                Ana Sayfaya Dön
              </Link>
            </Button>
          </div>
          
          <p className="text-sm text-gray-600">
            Sorularınız için{' '}
            <Link href="/contact" className="text-blue-600 hover:underline">
              iletişime geçebilirsiniz
            </Link>
          </p>
        </div>

        {/* Additional Info */}
        <div className="mt-12 p-6 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">
            Önemli Bilgiler
          </h3>
          <div className="text-sm text-blue-800 space-y-1">
            <p>• Siparişinizle ilgili güncellemeler e-posta ile bildirilecektir</p>
            <p>• Kargo takip numaranızı e-posta ile alacaksınız</p>
            <p>• Teslimat süresi 2-5 iş günüdür</p>
            <p>• 14 gün içinde koşulsuz iade hakkınız bulunmaktadır</p>
          </div>
        </div>
      </div>
    </div>
  )
}
