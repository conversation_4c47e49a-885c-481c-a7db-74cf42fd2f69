'use client'

import Image from 'next/image'
import Link from 'next/link'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { ProductCarousel } from '@/components/ui/product-carousel'
import { ShoppingCart, Heart, Star } from 'lucide-react'
import { Product } from '@/lib/products'
import { addToCart } from '@/lib/cart'
import { getCurrentUser } from '@/lib/auth'
import { toast } from 'react-hot-toast'

interface ProductCardProps {
  product: Product
}

export default function ProductCard({ product }: ProductCardProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isWishlisted, setIsWishlisted] = useState(false)

  const primaryImage = product.images?.find(img => img.is_primary) || product.images?.[0]
  const isInStock = (product.inventory?.quantity || 0) > 0
  const discountPercentage = product.compare_price 
    ? Math.round(((product.compare_price - product.price) / product.compare_price) * 100)
    : 0

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (!isInStock) {
      toast.error('Ürün stokta yok')
      return
    }

    setIsLoading(true)
    try {
      const user = await getCurrentUser()
      if (!user) {
        toast.error('Sepete eklemek için giriş yapmalısınız')
        return
      }

      await addToCart(user.id, product.id, 1)
      toast.success('Ürün sepete eklendi')
    } catch (error) {
      toast.error('Ürün sepete eklenemedi')
    } finally {
      setIsLoading(false)
    }
  }

  const handleWishlist = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsWishlisted(!isWishlisted)
    toast.success(isWishlisted ? 'Favorilerden çıkarıldı' : 'Favorilere eklendi')
  }

  return (
    <Card className="group relative overflow-hidden border-0 shadow-sm hover:shadow-lg transition-shadow duration-300">
      <Link href={`/products/${product.slug}`}>
        <div className="relative">
          <ProductCarousel
            images={product.images || []}
            productName={product.name}
            autoPlay={true}
            autoPlayInterval={4000}
          />

          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1 z-10">
            {product.is_featured && (
              <Badge className="bg-brand text-brand-foreground hover:bg-brand/90">Öne Çıkan</Badge>
            )}
            {discountPercentage > 0 && (
              <Badge variant="destructive">%{discountPercentage} İndirim</Badge>
            )}
            {!isInStock && (
              <Badge variant="outline" className="bg-surface text-surface-foreground">Stokta Yok</Badge>
            )}
          </div>

          {/* Wishlist Button */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 bg-white/80 hover:bg-white opacity-0 group-hover:opacity-100 transition-opacity z-10"
            onClick={handleWishlist}
          >
            <Heart className={`h-4 w-4 ${isWishlisted ? 'fill-red-500 text-red-500' : ''}`} />
          </Button>

          {/* Quick Add to Cart */}
          <div className="absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
            <Button
              onClick={handleAddToCart}
              disabled={!isInStock || isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700"
              size="sm"
            >
              <ShoppingCart className="mr-2 h-4 w-4" />
              {isLoading ? 'Ekleniyor...' : 'Sepete Ekle'}
            </Button>
          </div>
        </div>

        <CardContent className="p-4">
          <div className="space-y-2">
            {/* Category */}
            {product.category && (
              <p className="text-xs text-gray-500 uppercase tracking-wide">
                {product.category.name}
              </p>
            )}

            {/* Title */}
            <h3 className="font-medium text-sm text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors">
              {product.name}
            </h3>

            {/* Description */}
            <p className="text-xs text-gray-600 line-clamp-2">
              {product.short_description}
            </p>

            {/* Rating */}
            <div className="flex items-center space-x-1">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-3 w-3 ${
                      i < 4 ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-xs text-gray-500">(24)</span>
            </div>

            {/* Price */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="font-bold text-lg text-gray-900">
                  {product.price.toLocaleString('tr-TR')}₺
                </span>
                {product.compare_price && (
                  <span className="text-sm text-gray-500 line-through">
                    {product.compare_price.toLocaleString('tr-TR')}₺
                  </span>
                )}
              </div>
            </div>

            {/* Stock Status */}
            <div className="text-xs">
              {isInStock ? (
                <span className="text-green-600">✓ Stokta ({product.inventory?.quantity} adet)</span>
              ) : (
                <span className="text-red-600">✗ Stokta yok</span>
              )}
            </div>
          </div>
        </CardContent>
      </Link>
    </Card>
  )
}