import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseAdmin } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabaseAdmin = createSupabaseAdmin()
    
    // Test if site_settings table exists by trying to query it
    const { data, error } = await supabaseAdmin
      .from('site_settings')
      .select('*')
      .limit(1)

    if (error) {
      return NextResponse.json({
        success: false,
        error: error.message,
        table_exists: false
      })
    }

    return NextResponse.json({
      success: true,
      table_exists: true,
      data: data
    })
  } catch (error) {
    console.error('Error testing settings table:', error)
    return NextResponse.json({
      success: false,
      error: 'Test failed',
      table_exists: false
    })
  }
}
