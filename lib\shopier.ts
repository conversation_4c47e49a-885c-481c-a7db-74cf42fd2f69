import crypto from 'crypto'

// Shopier configuration
export const shopierConfig = {
  apiKey: process.env.SHOPIER_API_KEY!,
  secret: process.env.SHOPIER_SECRET!,
  paymentUrl: process.env.SHOPIER_PAYMENT_URL || 'https://www.shopier.com/ShowProduct/api_pay4.php',
  websiteIndex: process.env.SHOPIER_WEBSITE_INDEX || '1'
}

// Currency mapping
export const currencyMap = {
  'TRY': 0,
  'USD': 1,
  'EUR': 2
}

// Product type mapping
export const productTypeMap = {
  'physical': 0,
  'digital': 1,
  'mixed': 2
}

// Shopier payment data interface
export interface ShopierPaymentData {
  orderId: string
  userId: string
  userEmail: string
  userFirstName: string
  userLastName: string
  userPhone?: string
  userAccountAge: number
  billingAddress: {
    address: string
    city: string
    country: string
    postcode: string
  }
  shippingAddress: {
    address: string
    city: string
    country: string
    postcode: string
  }
  products: Array<{
    id: string
    name: string
    quantity: number
    price: number
    discountPrice?: number
    subtotalPrice: number
    totalPrice: number
    productType: number
  }>
  orderInfo: {
    subtotal: number
    shippingCost: number
    total: number
    currency: string
    discountTotal?: number
    taxTotal?: number
  }
  useShippingAddress?: boolean
}

// Generate signature for Shopier
export function generateShopierSignature(
  randomNumber: string,
  orderId: string,
  totalAmount: number,
  currency: number,
  secret: string
): string {
  const data = `${randomNumber}${orderId}${totalAmount}${currency}`
  const signature = crypto.createHmac('sha256', secret).update(data).digest()
  return Buffer.from(signature).toString('base64')
}

// Verify Shopier callback signature
export function verifyShopierCallback(
  randomNumber: string,
  orderId: string,
  signature: string,
  secret: string
): boolean {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(`${randomNumber}${orderId}`)
    .digest()
  
  const receivedSignature = Buffer.from(signature, 'base64')
  
  return crypto.timingSafeEqual(expectedSignature, receivedSignature)
}

// Sanitize text for Shopier (similar to PHP saveText function)
export function sanitizeText(text: string): string {
  return text
    .replace(/'/g, '&apos;')
    .replace(/"/g, '&quot;')
    .replace(/>/g, '&gt;')
    .replace(/</g, '&lt;')
    .replace(/&/g, '&amp;')
}

// Generate random number for Shopier
export function generateRandomNumber(): string {
  return Math.floor(Math.random() * (999999 - 100000 + 1) + 100000).toString()
}

// Create Shopier payment form data
export function createShopierPaymentData(paymentData: ShopierPaymentData): Record<string, string> {
  const randomNumber = generateRandomNumber()
  const currency = currencyMap[paymentData.orderInfo.currency as keyof typeof currencyMap] || 0
  
  // Determine product type (0: physical, 1: digital, 2: mixed)
  let productType = 0
  const hasDigital = paymentData.products.some(p => p.productType === 1)
  const hasPhysical = paymentData.products.some(p => p.productType === 0)
  
  if (hasDigital && hasPhysical) {
    productType = 2
  } else if (hasDigital) {
    productType = 1
  } else {
    productType = 0
  }

  // Prepare product names
  const productNames = paymentData.products
    .map(p => p.name.replace(/['"&]/g, ''))
    .join(';')

  // Prepare product info JSON
  const productInfo = JSON.stringify(paymentData.products.map(product => ({
    name: product.name.trim(),
    product_id: product.id,
    product_type: product.productType,
    quantity: product.quantity,
    price: product.price,
    discount_price: product.discountPrice || null,
    subtotal_price: product.subtotalPrice,
    total_price: product.totalPrice
  })))

  // Prepare general info JSON
  const generalInfo = JSON.stringify({
    discount_total: paymentData.orderInfo.discountTotal || 0,
    shipping_total: paymentData.orderInfo.shippingCost,
    total: paymentData.orderInfo.total,
    total_tax: paymentData.orderInfo.taxTotal || 0
  })

  // Choose address based on useShippingAddress setting
  const useShippingAddress = paymentData.useShippingAddress || false
  const primaryAddress = useShippingAddress ? paymentData.shippingAddress : paymentData.billingAddress
  const secondaryAddress = useShippingAddress ? paymentData.billingAddress : paymentData.shippingAddress

  const formData = {
    API_key: shopierConfig.apiKey,
    website_index: shopierConfig.websiteIndex,
    use_adress: useShippingAddress ? '1' : '0',
    platform_order_id: paymentData.orderId,
    product_info: productInfo,
    general_info: generalInfo,
    product_name: productNames,
    product_type: productType.toString(),
    buyer_name: paymentData.userFirstName,
    buyer_surname: paymentData.userLastName,
    buyer_email: paymentData.userEmail,
    buyer_account_age: paymentData.userAccountAge.toString(),
    buyer_id_nr: paymentData.userId,
    buyer_phone: paymentData.userPhone || '',
    billing_address: primaryAddress.address,
    billing_city: primaryAddress.city,
    billing_country: primaryAddress.country,
    billing_postcode: primaryAddress.postcode,
    shipping_address: secondaryAddress.address,
    shipping_city: secondaryAddress.city,
    shipping_country: secondaryAddress.country,
    shipping_postcode: secondaryAddress.postcode,
    total_order_value: paymentData.orderInfo.total.toString(),
    currency: currency.toString(),
    platform: '0', // 0 for custom platform
    is_in_frame: '0',
    current_language: '0', // 0 for Turkish
    modul_version: '2.0.0',
    random_nr: randomNumber
  }

  // Generate signature
  const signature = generateShopierSignature(
    randomNumber,
    paymentData.orderId,
    paymentData.orderInfo.total,
    currency,
    shopierConfig.secret
  )

  formData.signature = signature

  // Sanitize all form data
  const sanitizedFormData: Record<string, string> = {}
  Object.entries(formData).forEach(([key, value]) => {
    sanitizedFormData[key] = sanitizeText(value.toString())
  })

  return sanitizedFormData
}

// Shopier callback response interface
export interface ShopierCallbackData {
  platform_order_id: string
  status: string
  payment_id: string
  installment: string
  random_nr: string
  signature: string
  error_message?: string
}

// Process Shopier callback
export function processShopierCallback(callbackData: ShopierCallbackData): {
  success: boolean
  orderId: string
  paymentId?: string
  installment?: string
  errorMessage?: string
} {
  const { platform_order_id, status, payment_id, installment, random_nr, signature, error_message } = callbackData

  // Verify signature
  const isSignatureValid = verifyShopierCallback(
    random_nr,
    platform_order_id,
    signature,
    shopierConfig.secret
  )

  if (!isSignatureValid) {
    return {
      success: false,
      orderId: platform_order_id,
      errorMessage: 'Security Error. Illegal access detected'
    }
  }

  const isSuccess = status.toLowerCase() === 'success'

  return {
    success: isSuccess,
    orderId: platform_order_id,
    paymentId: isSuccess ? payment_id : undefined,
    installment: isSuccess ? installment : undefined,
    errorMessage: isSuccess ? undefined : (error_message || 'Payment failed')
  }
}

// Shopier language texts
export const shopierTexts = {
  tr: {
    'Pay with Credit Card': 'Kredi Kartı ile Ödeme',
    'Pay securely by Shopier Module.': 'Kredi kartı ile güvenle ödeyin.',
    'Thank you for your order, please click the button below to pay with Credit Card.': 'Siparişiniz için teşekkürler, kredi kartı ile güvenli ödeme yapmak için tıklayınız.',
    'Pay via Shopier': 'Kredi kartı ile öde',
    'Cancel order & restore cart': 'Siparişi iptal edin',
    'Thank you for your order. We are now redirecting you to Payment Gateway to make payment': 'Siparişiniz için teşekkürler, ödeme sayfasına yönlendiriliyorsunuz',
    'Thank you for shopping with us. Your account has been charged and your transaction is successful.': 'Siparişiniz için teşekkürler, ödemeniz alınmıştır.',
    'Shopier payment successful': 'Shopier ödeme başarılı',
    'An error occurred in payment.The transaction has been declined.': 'Ödeme esnasında bir hata oluştu, işlem reddedildi.',
    'Security Error. Illegal access detected': 'Güvenlik hatası, yetkisiz erişim!'
  },
  en: {
    'Pay with Credit Card': 'Pay with Credit Card',
    'Pay securely by Shopier Module.': 'Pay securely by Shopier Module.',
    'Thank you for your order, please click the button below to pay with Credit Card.': 'Thank you for your order, please click the button below to pay with Credit Card.',
    'Pay via Shopier': 'Pay via Shopier',
    'Cancel order & restore cart': 'Cancel order & restore cart',
    'Thank you for your order. We are now redirecting you to Payment Gateway to make payment': 'Thank you for your order. We are now redirecting you to Payment Gateway to make payment',
    'Thank you for shopping with us. Your account has been charged and your transaction is successful.': 'Thank you for shopping with us. Your account has been charged and your transaction is successful.',
    'Shopier payment successful': 'Shopier payment successful',
    'An error occurred in payment.The transaction has been declined.': 'An error occurred in payment. The transaction has been declined.',
    'Security Error. Illegal access detected': 'Security Error. Illegal access detected'
  }
}

// Get localized text
export function getShopierText(key: string, locale: string = 'tr'): string {
  const texts = shopierTexts[locale as keyof typeof shopierTexts] || shopierTexts.tr
  return texts[key as keyof typeof texts] || key
}
