-- Fix orders table structure to match application requirements

-- Add missing payment_method column
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS payment_method text DEFAULT 'cash-on-delivery';

-- Add missing shipping_cost column (alias for shipping_amount)
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS shipping_cost decimal(10,2) DEFAULT 0;

-- Add missing total column (alias for total_amount)  
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS total decimal(10,2);

-- Update existing data to use new columns
UPDATE orders 
SET 
  shipping_cost = COALESCE(shipping_amount, 0),
  total = COALESCE(total_amount, subtotal + COALESCE(tax_amount, 0) + COALESCE(shipping_amount, 0))
WHERE shipping_cost IS NULL OR total IS NULL;

-- Add constraints
ALTER TABLE orders 
ADD CONSTRAINT IF NOT EXISTS check_total_positive CHECK (total > 0);

ALTER TABLE orders 
ADD CONSTRAINT IF NOT EXISTS check_subtotal_positive CHECK (subtotal > 0);

-- Add index for payment method
CREATE INDEX IF NOT EXISTS idx_orders_payment_method ON orders(payment_method);
