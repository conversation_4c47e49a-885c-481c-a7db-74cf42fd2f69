const fs = require('fs');

// Simple CSV parser for testing
function parseCSVLine(line) {
  const result = [];
  let current = '';
  let inQuotes = false;
  let i = 0;
  
  while (i < line.length) {
    const char = line[i];
    
    if (char === '"') {
      if (inQuotes && i + 1 < line.length && line[i + 1] === '"') {
        // <PERSON>le escaped quotes
        current += '"';
        i += 2;
        continue;
      } else {
        inQuotes = !inQuotes;
      }
    } else if (char === ',' && !inQuotes) {
      result.push(current.trim());
      current = '';
      i++;
      continue;
    } else {
      current += char;
    }
    i++;
  }
  
  result.push(current.trim());
  return result;
}

function parseCSV(csvContent) {
  const lines = csvContent.split('\n').filter(line => line.trim());
  if (lines.length < 2) return [];

  const headers = parseCSVLine(lines[0]);
  const rows = [];

  for (let i = 1; i < lines.length; i++) {
    const values = parseCSVLine(lines[i]);
    if (values.length >= headers.length - 5) { // Allow some flexibility
      const row = {};
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      rows.push(row);
    }
  }

  return rows;
}

// Test the CSV parsing
try {
  const csvContent = fs.readFileSync('products.csv', 'utf-8');
  const rows = parseCSV(csvContent);
  
  console.log('Total rows parsed:', rows.length);
  console.log('Headers:', Object.keys(rows[0] || {}));
  
  // Show first few products
  const mainProducts = rows.filter(row => {
    const type = row['Tür'] || '';
    return type === 'simple' || type === 'variable';
  });
  
  console.log('Main products found:', mainProducts.length);
  
  mainProducts.slice(0, 3).forEach((product, index) => {
    console.log(`\nProduct ${index + 1}:`);
    console.log('Name:', product['İsim']);
    console.log('SKU:', product['Stok kodu (SKU)']);
    console.log('Price:', product['Normal fiyat']);
    console.log('Type:', product['Tür']);
    console.log('Categories:', product['Kategoriler']);
  });
  
} catch (error) {
  console.error('Error:', error.message);
}
