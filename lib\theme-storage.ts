const THEME_STORAGE_KEY = '3dunyam-theme'

export const saveThemeToStorage = (themeId: string): void => {
  try {
    if (typeof window !== 'undefined') {
      localStorage.setItem(THEME_STORAGE_KEY, themeId)
    }
  } catch (error) {
    console.error('Error saving theme to localStorage:', error)
  }
}

export const loadThemeFromStorage = (): string | null => {
  try {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(THEME_STORAGE_KEY)
    }
  } catch (error) {
    console.error('Error loading theme from localStorage:', error)
  }
  return null
}

export const removeThemeFromStorage = (): void => {
  try {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(THEME_STORAGE_KEY)
    }
  } catch (error) {
    console.error('Error removing theme from localStorage:', error)
  }
}

export const isStorageAvailable = (): boolean => {
  try {
    if (typeof window === 'undefined') return false
    
    const test = '__storage_test__'
    localStorage.setItem(test, test)
    localStorage.removeItem(test)
    return true
  } catch {
    return false
  }
}
