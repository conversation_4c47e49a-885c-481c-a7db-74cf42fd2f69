'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import ProductCard from '@/components/product/ProductCard'
import { getFeaturedProducts, getCategories } from '@/lib/products'
import { ArrowRight, Truck, Shield, Headphones, Award, Star } from 'lucide-react'

export default function HomePage() {
  const [featuredProducts, setFeaturedProducts] = useState<any[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      const [products, cats] = await Promise.all([
        getFeaturedProducts(8),
        getCategories()
      ])

      // If no featured products, get regular products
      if (products.length === 0) {
        const { getProducts } = await import('@/lib/products')
        const regularProducts = await getProducts({ limit: 8 })
        setFeaturedProducts(regularProducts)
      } else {
        setFeaturedProducts(products)
      }

      setCategories(cats.slice(0, 4))
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const features = [
    {
      icon: Truck,
      title: 'Ücretsiz Kargo',
      description: '500₺ üzeri alışverişlerde ücretsiz kargo'
    },
    {
      icon: Shield,
      title: 'Güvenli Alışveriş',
      description: 'SSL sertifikası ile korumalı ödeme'
    },
    {
      icon: Headphones,
      title: '7/24 Destek',
      description: 'Uzman ekibimiz her zaman yanınızda'
    },
    {
      icon: Award,
      title: 'Kalite Garantisi',
      description: 'Orijinal ürünler, kalite garantisi'
    }
  ]

  const testimonials = [
    {
      name: 'Ahmet Yılmaz',
      rating: 5,
      comment: 'Harika bir platform! Ürünler çok kaliteli ve teslimat super hızlı.',
      product: 'Ender 3 V2 3D Yazıcı'
    },
    {
      name: 'Elif Demir',
      rating: 5,
      comment: 'Filament kalitesi mükemmel, baskı sonuçları çok başarılı.',
      product: 'PLA Filament Paketi'
    },
    {
      name: 'Murat Kaya',
      rating: 5,
      comment: 'Müşteri hizmetleri çok ilgili, her soruma hızlıca cevap aldım.',
      product: 'Reçine 3D Yazıcı'
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-800 text-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-y-12"></div>
        </div>

        <div className="container mx-auto px-4 py-20 lg:py-32 relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8 animate-fade-in">
              <div className="space-y-4">
                <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-sm font-medium">
                  🚀 Türkiye'nin #1 3D Baskı Platformu
                </div>
                <h1 className="text-4xl lg:text-7xl font-bold leading-tight">
                  <span className="block">3D Dünyamla</span>
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500">
                    Hayallerinizi
                  </span>
                  <span className="block">Gerçeğe Dönüştürün</span>
                </h1>
              </div>

              <p className="text-xl text-blue-100 leading-relaxed max-w-lg">
                Profesyonel 3D yazıcılar, premium filamentler ve uzman desteği ile
                projelerinizi hayata geçirin. Kalite, hız ve güvenilirlik bir arada.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button asChild size="lg" className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black font-semibold hover:from-yellow-500 hover:to-orange-600 shadow-lg hover:shadow-xl transition-all duration-300">
                  <Link href="/products">
                    <span className="flex items-center">
                      Ürünleri Keşfet
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </span>
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm">
                  <Link href="/contact">Hemen Sipariş Ver</Link>
                </Button>
              </div>

              {/* Stats */}
              <div className="flex items-center gap-8 pt-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-yellow-400">1000+</div>
                  <div className="text-sm text-blue-200">Mutlu Müşteri</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400">500+</div>
                  <div className="text-sm text-blue-200">Ürün Çeşidi</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-400">24/7</div>
                  <div className="text-sm text-blue-200">Destek</div>
                </div>
              </div>
            </div>
            <div className="relative lg:flex justify-center">
              <div className="relative">
                {/* Main Hero Image */}
                <div className="relative rounded-3xl overflow-hidden shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-500">
                  <Image
                    src="https://images.pexels.com/photos/3846055/pexels-photo-3846055.jpeg"
                    alt="3D Yazıcı ile Üretim"
                    width={600}
                    height={400}
                    className="w-full h-auto"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                </div>

                {/* Floating Cards */}
                <div className="absolute -bottom-8 -left-8 bg-white/95 backdrop-blur-sm text-gray-900 p-6 rounded-2xl shadow-xl border border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-lg">✓</span>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">%99</div>
                      <div className="text-sm text-gray-600">Müşteri Memnuniyeti</div>
                    </div>
                  </div>
                </div>

                <div className="absolute -top-8 -right-8 bg-white/95 backdrop-blur-sm text-gray-900 p-6 rounded-2xl shadow-xl border border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-lg">🚀</span>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-purple-600">24h</div>
                      <div className="text-sm text-gray-600">Hızlı Teslimat</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
                    <feature.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-lg mb-2">{feature.title}</h3>
                  <p className="text-gray-600 text-sm">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Popüler Kategoriler</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              3D baskı dünyasında ihtiyacınız olan her şey burada. 
              Kategorilerimizi keşfedin ve projelerinize başlayın.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {categories.map((category) => (
              <Link key={category.id} href={`/category/${category.slug}`}>
                <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer">
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src={category.image_url || 'https://images.pexels.com/photos/3846055/pexels-photo-3846055.jpeg'}
                      alt={category.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-black/40 group-hover:bg-black/50 transition-colors" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center text-white">
                        <h3 className="font-bold text-xl mb-2">{category.name}</h3>
                        <p className="text-sm opacity-90">{category.description}</p>
                      </div>
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-end mb-12">
            <div>
              <h2 className="text-3xl font-bold mb-4">Öne Çıkan Ürünler</h2>
              <p className="text-gray-600">En popüler ve kaliteli ürünlerimizi keşfedin</p>
            </div>
            <Button asChild variant="outline">
              <Link href="/products">
                <span className="flex items-center">
                  Tümünü Gör
                  <ArrowRight className="ml-2 h-4 w-4" />
                </span>
              </Link>
            </Button>
          </div>

          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-200 aspect-square rounded-lg mb-4"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {featuredProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Müşterilerimiz Ne Diyor?</h2>
            <p className="text-gray-600">Binlerce mutlu müşterimizden gelen yorumlar</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-6">
                <CardContent className="p-0">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <blockquote className="text-gray-700 mb-4">
                    "{testimonial.comment}"
                  </blockquote>
                  <div>
                    <div className="font-semibold">{testimonial.name}</div>
                    <div className="text-sm text-gray-500">{testimonial.product}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Haberdar Olun</h2>
          <p className="text-blue-100 mb-8 max-w-2xl mx-auto">
            Yeni ürünler, özel indirimler ve 3D baskı dünyasındaki 
            gelişmelerden haberdar olmak için bültenimize katılın.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="E-posta adresiniz"
              className="flex-1 px-4 py-3 rounded-lg text-gray-900"
            />
            <Button className="bg-white text-blue-600 hover:bg-gray-100">
              Abone Ol
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}