'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
// Orders will be loaded via API
import { toast } from 'react-hot-toast'
import { 
  Package, 
  Search, 
  Eye, 
  MoreHorizontal,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  Truck,
  RefreshCw,
  Download
} from 'lucide-react'

export default function AdminOrdersPage() {
  const [orders, setOrders] = useState<any[]>([])
  const [filteredOrders, setFilteredOrders] = useState<any[]>([])
  const [stats, setStats] = useState<any>({})
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [updatingOrderId, setUpdatingOrderId] = useState<string | null>(null)
  const [isCreatingOrder, setIsCreatingOrder] = useState(false)

  useEffect(() => {
    loadOrders()
    loadStats()
  }, [])

  useEffect(() => {
    // Filter orders based on search query and status
    let filtered = orders

    if (searchQuery) {
      filtered = filtered.filter(order =>
        order.order_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.user?.first_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.user?.last_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.user?.email?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    setFilteredOrders(filtered)
  }, [orders, searchQuery, statusFilter])

  const loadOrders = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/admin/orders')

      if (!response.ok) {
        throw new Error('Failed to fetch orders')
      }

      const result = await response.json()

      if (result.success) {
        console.log('Orders loaded:', result.data)
        setOrders(result.data || [])
      } else {
        throw new Error(result.error || 'Failed to load orders')
      }
    } catch (error) {
      console.error('Error loading orders:', error)
      toast.error('Siparişler yüklenemedi')
      setOrders([])
    } finally {
      setIsLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await fetch('/api/admin/stats')

      if (!response.ok) {
        throw new Error('Failed to fetch stats')
      }

      const result = await response.json()
      setStats(result)
    } catch (error) {
      console.error('Error loading stats:', error)
      // Fallback to empty stats
      setStats({
        total_orders: 0,
        pending_orders: 0,
        confirmed_orders: 0,
        delivered_orders: 0,
        total_revenue: 0
      })
    }
  }

  const handleStatusUpdate = async (orderId: string, newStatus: string) => {
    setUpdatingOrderId(orderId)
    try {
      const response = await fetch('/api/admin/orders', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          orderId,
          status: newStatus
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update order status')
      }

      const result = await response.json()

      if (result.success) {
        toast.success('Sipariş durumu güncellendi')
        await loadOrders() // Reload orders
        await loadStats() // Reload stats
      } else {
        throw new Error(result.error || 'Failed to update order status')
      }
    } catch (error) {
      console.error('Error updating order status:', error)
      toast.error('Sipariş durumu güncellenemedi')
    } finally {
      setUpdatingOrderId(null)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'confirmed': return 'bg-blue-100 text-blue-800'
      case 'shipped': return 'bg-purple-100 text-purple-800'
      case 'delivered': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Beklemede'
      case 'confirmed': return 'Onaylandı'
      case 'shipped': return 'Kargoda'
      case 'delivered': return 'Teslim Edildi'
      case 'cancelled': return 'İptal Edildi'
      default: return status
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return Clock
      case 'confirmed': return CheckCircle
      case 'shipped': return Truck
      case 'delivered': return CheckCircle
      case 'cancelled': return XCircle
      default: return Package
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Sipariş Yönetimi</h1>
          <p className="text-gray-600 mt-2">
            Siparişleri görüntüleyin, durumlarını güncelleyin ve takip edin
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadOrders}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Yenile
          </Button>
          <Button variant="outline" onClick={async () => {
            setIsCreatingOrder(true)
            try {
              const response = await fetch('/api/test/create-order', { method: 'POST' })
              const result = await response.json()
              if (result.success) {
                toast.success('Test siparişi oluşturuldu')
                loadOrders()
              } else {
                toast.error('Test siparişi oluşturulamadı')
              }
            } catch (error) {
              toast.error('Hata oluştu')
            } finally {
              setIsCreatingOrder(false)
            }
          }} disabled={isCreatingOrder}>
            {isCreatingOrder ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Package className="mr-2 h-4 w-4" />
            )}
            Test Siparişi Oluştur
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Rapor İndir
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Toplam Sipariş</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_orders || 0}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Beklemede</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending_orders || 0}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Onaylandı</p>
                <p className="text-2xl font-bold text-blue-600">{stats.confirmed_orders || 0}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Teslim Edildi</p>
                <p className="text-2xl font-bold text-green-600">{stats.delivered_orders || 0}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Toplam Gelir</p>
                <p className="text-2xl font-bold text-gray-900">
                  {(stats.total_revenue || 0).toLocaleString('tr-TR')} ₺
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Sipariş numarası, müşteri adı veya e-posta ile ara..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm Durumlar</SelectItem>
                <SelectItem value="pending">Beklemede</SelectItem>
                <SelectItem value="confirmed">Onaylandı</SelectItem>
                <SelectItem value="shipped">Kargoda</SelectItem>
                <SelectItem value="delivered">Teslim Edildi</SelectItem>
                <SelectItem value="cancelled">İptal Edildi</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            Siparişler ({filteredOrders.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Sipariş No</TableHead>
                  <TableHead>Müşteri</TableHead>
                  <TableHead>Tarih</TableHead>
                  <TableHead>Ürün Sayısı</TableHead>
                  <TableHead>Toplam</TableHead>
                  <TableHead>Durum</TableHead>
                  <TableHead>Ödeme</TableHead>
                  <TableHead className="w-16">İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOrders.map((order) => {
                  const StatusIcon = getStatusIcon(order.status)
                  
                  return (
                    <TableRow key={order.id}>
                      <TableCell>
                        <Link 
                          href={`/admin/orders/${order.id}`}
                          className="font-medium text-blue-600 hover:text-blue-800"
                        >
                          #{order.order_number}
                        </Link>
                      </TableCell>
                      
                      <TableCell>
                        <div>
                          <p className="font-medium text-gray-900">
                            {order.user?.first_name && order.user?.last_name
                              ? `${order.user.first_name} ${order.user.last_name}`
                              : 'Kullanıcı Bilgisi Yok'
                            }
                          </p>
                          <p className="text-sm text-gray-600">
                            {order.user?.email || order.user_id}
                          </p>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div>
                          <p className="text-sm">
                            {new Date(order.created_at).toLocaleDateString('tr-TR')}
                          </p>
                          <p className="text-xs text-gray-600">
                            {new Date(order.created_at).toLocaleTimeString('tr-TR')}
                          </p>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <span className="text-sm">
                          {order.items?.length || 0} ürün
                        </span>
                      </TableCell>
                      
                      <TableCell>
                        <span className="font-medium">
                          {(order.total_amount || 0).toLocaleString('tr-TR')} ₺
                        </span>
                      </TableCell>
                      
                      <TableCell>
                        <Badge className={getStatusColor(order.status)}>
                          <StatusIcon className="mr-1 h-3 w-3" />
                          {getStatusText(order.status)}
                        </Badge>
                      </TableCell>

                      <TableCell>
                        <Badge variant="outline">
                          {order.payment_method === 'credit-card' ? 'Kredi Kartı' :
                           order.payment_method === 'bank-transfer' ? 'Havale/EFT' :
                           order.payment_method === 'cash-on-delivery' ? 'Kapıda Ödeme' :
                           order.payment_method}
                        </Badge>
                      </TableCell>

                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              disabled={updatingOrderId === order.id}
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/orders/${order.id}`}>
                                <Eye className="mr-2 h-4 w-4" />
                                Detayları Görüntüle
                              </Link>
                            </DropdownMenuItem>
                            
                            {order.status === 'pending' && (
                              <DropdownMenuItem
                                onClick={() => handleStatusUpdate(order.id, 'confirmed')}
                              >
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Onayla
                              </DropdownMenuItem>
                            )}
                            
                            {order.status === 'confirmed' && (
                              <DropdownMenuItem
                                onClick={() => handleStatusUpdate(order.id, 'shipped')}
                              >
                                <Truck className="mr-2 h-4 w-4" />
                                Kargoya Ver
                              </DropdownMenuItem>
                            )}
                            
                            {order.status === 'shipped' && (
                              <DropdownMenuItem
                                onClick={() => handleStatusUpdate(order.id, 'delivered')}
                              >
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Teslim Edildi
                              </DropdownMenuItem>
                            )}
                            
                            {['pending', 'confirmed'].includes(order.status) && (
                              <DropdownMenuItem
                                onClick={() => handleStatusUpdate(order.id, 'cancelled')}
                                className="text-red-600"
                              >
                                <XCircle className="mr-2 h-4 w-4" />
                                İptal Et
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          </div>
          
          {filteredOrders.length === 0 && (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchQuery || statusFilter !== 'all' ? 'Sipariş Bulunamadı' : 'Henüz Sipariş Yok'}
              </h3>
              <p className="text-gray-600">
                {searchQuery || statusFilter !== 'all'
                  ? 'Arama kriterlerinize uygun sipariş bulunamadı.'
                  : 'İlk siparişinizi bekliyorsunuz.'
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
