import { createSupabaseAdmin } from './supabase'
import { createProduct, createCategory, createProductImage, createInventory } from './products'

export interface CSVRow {
  [key: string]: string
}

export interface ImportResult {
  success: boolean
  message: string
  details?: {
    totalRows: number
    successfulImports: number
    failedImports: number
    errors: string[]
  }
}

// Function to generate slug from Turkish text
export const generateSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/ğ/g, 'g')
    .replace(/ü/g, 'u')
    .replace(/ş/g, 's')
    .replace(/ı/g, 'i')
    .replace(/ö/g, 'o')
    .replace(/ç/g, 'c')
    .replace(/[^a-z0-9\s-]/g, '')
    .trim()
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
}

// Function to parse CSV content with proper quote handling
export const parseCSV = (csvContent: string): CSVRow[] => {
  const lines = csvContent.split('\n').filter(line => line.trim())
  if (lines.length < 2) return []

  const headers = parseCSVLine(lines[0])
  const rows: CSVRow[] = []

  for (let i = 1; i < lines.length; i++) {
    const values = parseCSVLine(lines[i])
    if (values.length >= headers.length - 5) { // Allow some flexibility in column count
      const row: CSVRow = {}
      headers.forEach((header, index) => {
        row[header] = values[index] || ''
      })
      rows.push(row)
    }
  }

  return rows
}

// Function to parse a single CSV line handling commas within quotes
const parseCSVLine = (line: string): string[] => {
  const result: string[] = []
  let current = ''
  let inQuotes = false
  let i = 0

  while (i < line.length) {
    const char = line[i]

    if (char === '"') {
      if (inQuotes && i + 1 < line.length && line[i + 1] === '"') {
        // Handle escaped quotes
        current += '"'
        i += 2
        continue
      } else {
        inQuotes = !inQuotes
      }
    } else if (char === ',' && !inQuotes) {
      result.push(current.trim())
      current = ''
      i++
      continue
    } else {
      current += char
    }
    i++
  }

  result.push(current.trim())
  return result
}

// Function to create or get category
export const createOrGetCategory = async (categoryPath: string): Promise<string | null> => {
  if (!categoryPath) return null

  // Split category path and get the main category
  const categories = categoryPath.split(',').map(cat => cat.trim())
  const mainCategory = categories[0]

  if (!mainCategory) return null

  const slug = generateSlug(mainCategory)

  // Check if category exists
  const supabaseAdmin = createSupabaseAdmin()
  const { data: existingCategory } = await supabaseAdmin
    .from('categories')
    .select('id')
    .eq('slug', slug)
    .single()

  if (existingCategory) {
    return existingCategory.id
  }

  // Create new category
  try {
    const newCategory = await createCategory({
      name: mainCategory,
      slug: slug,
      description: `${mainCategory} kategorisi`,
      is_active: true,
      sort_order: 0
    })
    return newCategory.id
  } catch (error) {
    console.error('Error creating category:', error)
    return null
  }
}

// Function to process product images
export const processProductImages = async (productId: string, imageUrls: string): Promise<void> => {
  if (!imageUrls) return

  const urls = imageUrls.split(',').map(url => url.trim()).filter(url => url)
  
  for (let i = 0; i < urls.length; i++) {
    try {
      await createProductImage({
        product_id: productId,
        image_url: urls[i],
        alt_text: '',
        sort_order: i,
        is_primary: i === 0
      })
    } catch (error) {
      console.error('Error creating product image:', error)
    }
  }
}

// Function to map CSV row to product data
export const mapCSVRowToProduct = async (row: CSVRow): Promise<any> => {
  const name = row['İsim'] || ''
  const sku = row['Stok kodu (SKU)'] || row['Kimlik'] || '' // Use ID as fallback SKU
  const price = parseFloat(row['Normal fiyat'] || '0')
  const description = row['Açıklama'] || ''
  const shortDescription = row['Kısa açıklama'] || ''
  const categories = row['Kategoriler'] || ''
  const isFeatured = (row['Öne çıkan?'] || '0') === '1'
  const isActive = (row['Yayımlanmış'] || '1') === '1'

  // Clean up description - remove HTML tags and extra whitespace
  const cleanDescription = description
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/\n/g, ' ') // Replace newlines with spaces
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim()

  const cleanShortDescription = shortDescription
    .replace(/<[^>]*>/g, '')
    .replace(/\n/g, ' ')
    .replace(/\s+/g, ' ')
    .trim()

  if (!name || !sku) {
    throw new Error(`Geçersiz ürün verisi: ${name || 'İsimsiz'} - ${sku || 'SKU yok'}`)
  }

  // Generate unique SKU if needed
  const finalSku = sku || `PROD-${row['Kimlik'] || Date.now()}`
  const slug = generateSlug(name)
  const categoryId = await createOrGetCategory(categories)

  return {
    name,
    slug,
    description: cleanDescription,
    short_description: cleanShortDescription,
    price: price > 0 ? price : 100, // Default price if not specified
    sku: finalSku,
    category_id: categoryId,
    material_type: 'PLA+', // Default material type
    is_active: isActive,
    is_featured: isFeatured,
    seo_title: name,
    seo_description: cleanShortDescription || cleanDescription.substring(0, 160)
  }
}

// Main import function
export const importProductsFromCSV = async (csvContent: string): Promise<ImportResult> => {
  try {
    console.log('Starting CSV parsing...')
    const rows = parseCSV(csvContent)
    console.log(`Parsed ${rows.length} rows from CSV`)

    if (rows.length === 0) {
      return {
        success: false,
        message: 'CSV dosyası boş veya geçersiz format'
      }
    }

    let successfulImports = 0
    let failedImports = 0
    const errors: string[] = []

    // Filter out variation products for now, only process simple and variable products
    const mainProducts = rows.filter(row => {
      const type = row['Tür'] || row['Type'] || ''
      return type === 'simple' || type === 'variable'
    })

    console.log(`Found ${mainProducts.length} main products to import`)

    // Process only first 5 products for testing
    const productsToProcess = mainProducts.slice(0, 5)
    console.log(`Processing first ${productsToProcess.length} products for testing`)

    for (const row of productsToProcess) {
      try {
        console.log(`Processing product: ${row['İsim']}`)
        const productData = await mapCSVRowToProduct(row)
        console.log('Product data mapped:', productData)

        // Check if product with same SKU already exists
        const supabaseAdmin = createSupabaseAdmin()
        const { data: existingProduct } = await supabaseAdmin
          .from('products')
          .select('id')
          .eq('sku', productData.sku)
          .single()

        if (existingProduct) {
          errors.push(`Ürün zaten mevcut: ${productData.name} (SKU: ${productData.sku})`)
          failedImports++
          continue
        }

        // Create product
        console.log('Creating product...')
        const newProduct = await createProduct(productData)
        console.log('Product created:', newProduct.id)

        // Process images
        const imageUrls = row['Görseller'] || row['Images'] || ''
        if (imageUrls) {
          console.log('Processing images...')
          await processProductImages(newProduct.id, imageUrls)
        }

        // Create inventory record
        console.log('Creating inventory...')
        await createInventory({
          product_id: newProduct.id,
          quantity: 0, // Default quantity
          reserved_quantity: 0,
          reorder_level: 10
        })

        successfulImports++
        console.log(`Successfully imported: ${productData.name}`)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Bilinmeyen hata'
        console.error('Error importing product:', error)
        errors.push(`Satır ${productsToProcess.indexOf(row) + 2}: ${errorMessage}`)
        failedImports++
      }
    }

    return {
      success: successfulImports > 0,
      message: `İçe aktarma tamamlandı. ${successfulImports} ürün başarıyla eklendi, ${failedImports} ürün başarısız.`,
      details: {
        totalRows: productsToProcess.length,
        successfulImports,
        failedImports,
        errors
      }
    }
  } catch (error) {
    console.error('CSV import error:', error)
    return {
      success: false,
      message: 'CSV işleme sırasında hata oluştu: ' + (error instanceof Error ? error.message : 'Bilinmeyen hata')
    }
  }
}
