'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { getShopierText } from '@/lib/shopier'
import { toast } from 'react-hot-toast'
import { CreditCard, Shield, Lock } from 'lucide-react'

interface ShopierPaymentProps {
  orderId: string
  orderTotal: number
  onPaymentStart?: () => void
  onPaymentError?: (error: string) => void
  locale?: string
}

export default function ShopierPayment({ 
  orderId, 
  orderTotal, 
  onPaymentStart, 
  onPaymentError,
  locale = 'tr' 
}: ShopierPaymentProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [paymentData, setPaymentData] = useState<{
    paymentUrl: string
    formData: Record<string, string>
  } | null>(null)
  const formRef = useRef<HTMLFormElement>(null)

  const handlePayment = async () => {
    setIsLoading(true)
    onPaymentStart?.()

    try {
      const response = await fetch('/api/payment/shopier', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ orderId })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Payment initialization failed')
      }

      setPaymentData(result.data)
      
      // Auto-submit form after a short delay to show loading state
      setTimeout(() => {
        if (formRef.current) {
          formRef.current.submit()
        }
      }, 1000)

    } catch (error) {
      console.error('Payment error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Payment failed'
      toast.error(errorMessage)
      onPaymentError?.(errorMessage)
      setIsLoading(false)
    }
  }

  // Auto-submit form when payment data is available
  useEffect(() => {
    if (paymentData && formRef.current && !isLoading) {
      formRef.current.submit()
    }
  }, [paymentData, isLoading])

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <CreditCard className="h-5 w-5" />
          <span>{getShopierText('Pay with Credit Card', locale)}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Payment Info */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-600">Toplam Tutar:</span>
            <span className="font-semibold text-lg">
              {orderTotal.toLocaleString('tr-TR')} ₺
            </span>
          </div>
          <p className="text-xs text-gray-500">
            {getShopierText('Pay securely by Shopier Module.', locale)}
          </p>
        </div>

        {/* Security Features */}
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <div className="flex items-center space-x-1">
            <Shield className="h-4 w-4 text-green-600" />
            <span>SSL Güvenlik</span>
          </div>
          <div className="flex items-center space-x-1">
            <Lock className="h-4 w-4 text-green-600" />
            <span>256-bit Şifreleme</span>
          </div>
        </div>

        {/* Payment Button */}
        <Button 
          onClick={handlePayment}
          disabled={isLoading}
          className="w-full"
          size="lg"
        >
          {isLoading ? (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>
                {paymentData 
                  ? getShopierText('Thank you for your order. We are now redirecting you to Payment Gateway to make payment', locale)
                  : 'Ödeme Hazırlanıyor...'
                }
              </span>
            </div>
          ) : (
            getShopierText('Pay via Shopier', locale)
          )}
        </Button>

        {/* Hidden Form for Shopier */}
        {paymentData && (
          <form
            ref={formRef}
            action={paymentData.paymentUrl}
            method="POST"
            style={{ display: 'none' }}
          >
            {Object.entries(paymentData.formData).map(([key, value]) => (
              <input
                key={key}
                type="hidden"
                name={key}
                value={value}
              />
            ))}
          </form>
        )}

        {/* Loading Overlay */}
        {isLoading && paymentData && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg text-center max-w-sm mx-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h3 className="font-semibold mb-2">Ödeme Sayfasına Yönlendiriliyor</h3>
              <p className="text-sm text-gray-600">
                Lütfen bekleyin, güvenli ödeme sayfasına yönlendiriliyorsunuz...
              </p>
            </div>
          </div>
        )}

        {/* Payment Info */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>• Tüm kredi kartları kabul edilir</p>
          <p>• 3D Secure ile güvenli ödeme</p>
          <p>• Taksit seçenekleri mevcuttur</p>
          <p>• SSL sertifikası ile korunur</p>
        </div>
      </CardContent>
    </Card>
  )
}
