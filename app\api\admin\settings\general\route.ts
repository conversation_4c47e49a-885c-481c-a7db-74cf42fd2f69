import { NextRequest, NextResponse } from 'next/server'
import { getSetting, updateSetting } from '@/lib/settings'

export async function GET(request: NextRequest) {
  try {
    const [siteName, siteDescription, contactEmail, contactPhone] = await Promise.all([
      getSetting('site_name'),
      getSetting('site_description'),
      getSetting('contact_email'),
      getSetting('contact_phone')
    ])
    
    const settings = {
      site_name: siteName || '3Dünyam',
      site_description: siteDescription || '3D Baskı Ürünleri E-Ticaret Platformu',
      contact_email: contactEmail || '<EMAIL>',
      contact_phone: contactPhone || '+90 ************'
    }
    
    return NextResponse.json({
      success: true,
      data: settings
    })
  } catch (error) {
    console.error('Error fetching general settings:', error)
    return NextResponse.json(
      { error: 'Genel ayarlar yüklenemedi' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate the request body
    if (!body.site_name || !body.site_description || !body.contact_email || !body.contact_phone) {
      return NextResponse.json(
        { error: 'Gerekli alanlar eksik' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.contact_email)) {
      return NextResponse.json(
        { error: 'Geçersiz e-posta adresi' },
        { status: 400 }
      )
    }

    // Update all general settings
    await Promise.all([
      updateSetting('site_name', body.site_name, 'Site name'),
      updateSetting('site_description', body.site_description, 'Site description'),
      updateSetting('contact_email', body.contact_email, 'Contact email address'),
      updateSetting('contact_phone', body.contact_phone, 'Contact phone number')
    ])
    
    return NextResponse.json({
      success: true,
      message: 'Genel ayarlar başarıyla güncellendi'
    })
  } catch (error) {
    console.error('Error updating general settings:', error)
    return NextResponse.json(
      { error: 'Genel ayarlar güncellenemedi' },
      { status: 500 }
    )
  }
}
