import { createSupabaseAdmin } from './supabase'
import { Database } from './database.types'

export type InventoryItem = Database['public']['Tables']['inventory']['Row']

// Get products with inventory information
export const getProducts = async (filters?: {
  limit?: number
  category?: string
  search?: string
  lowStock?: boolean
  outOfStock?: boolean
}) => {
  const supabaseAdmin = createSupabaseAdmin()
  
  let query = supabaseAdmin
    .from('products')
    .select(`
      *,
      category:categories(*),
      images:product_images(*),
      inventory(*)
    `)
    .eq('is_active', true)
    .order('name')

  if (filters?.category) {
    query = query.eq('category_id', filters.category)
  }

  if (filters?.search) {
    query = query.or(`name.ilike.%${filters.search}%,sku.ilike.%${filters.search}%`)
  }

  if (filters?.limit) {
    query = query.limit(filters.limit)
  }

  const { data, error } = await query

  if (error) throw error

  let products = data || []

  // Apply client-side filters for inventory-based conditions
  if (filters?.lowStock) {
    products = products.filter(product => {
      const quantity = product.inventory?.quantity || 0
      const reorderLevel = product.inventory?.reorder_level || 10
      return quantity > 0 && quantity <= reorderLevel
    })
  }

  if (filters?.outOfStock) {
    products = products.filter(product => (product.inventory?.quantity || 0) === 0)
  }

  return products
}

// Update inventory for a product
export const updateInventory = async (productId: string, inventoryData: {
  quantity?: number
  reserved_quantity?: number
  reorder_level?: number
}) => {
  const supabaseAdmin = createSupabaseAdmin()
  
  // Check if inventory record exists
  const { data: existingInventory } = await supabaseAdmin
    .from('inventory')
    .select('id')
    .eq('product_id', productId)
    .single()

  if (existingInventory) {
    // Update existing inventory
    const { data, error } = await supabaseAdmin
      .from('inventory')
      .update({
        ...inventoryData,
        updated_at: new Date().toISOString()
      })
      .eq('product_id', productId)
      .select()
      .single()

    if (error) throw error
    return data
  } else {
    // Create new inventory record
    const { data, error } = await supabaseAdmin
      .from('inventory')
      .insert({
        product_id: productId,
        quantity: inventoryData.quantity || 0,
        reserved_quantity: inventoryData.reserved_quantity || 0,
        reorder_level: inventoryData.reorder_level || 10
      })
      .select()
      .single()

    if (error) throw error
    return data
  }
}

// Adjust inventory (add/subtract stock)
export const adjustInventory = async (productId: string, adjustment: number, reason?: string) => {
  const supabaseAdmin = createSupabaseAdmin()
  
  // Get current inventory
  const { data: currentInventory } = await supabaseAdmin
    .from('inventory')
    .select('quantity')
    .eq('product_id', productId)
    .single()

  if (!currentInventory) {
    throw new Error('Inventory record not found')
  }

  const newQuantity = Math.max(0, currentInventory.quantity + adjustment)

  // Update inventory
  const { data, error } = await supabaseAdmin
    .from('inventory')
    .update({
      quantity: newQuantity,
      updated_at: new Date().toISOString()
    })
    .eq('product_id', productId)
    .select()
    .single()

  if (error) throw error

  // Log inventory movement (if you have an inventory_movements table)
  // This would be useful for tracking stock changes
  
  return data
}

// Reserve inventory (for orders)
export const reserveInventory = async (productId: string, quantity: number) => {
  const supabaseAdmin = createSupabaseAdmin()
  
  // Get current inventory
  const { data: currentInventory } = await supabaseAdmin
    .from('inventory')
    .select('quantity, reserved_quantity')
    .eq('product_id', productId)
    .single()

  if (!currentInventory) {
    throw new Error('Inventory record not found')
  }

  const availableQuantity = currentInventory.quantity - currentInventory.reserved_quantity
  
  if (availableQuantity < quantity) {
    throw new Error('Insufficient inventory available')
  }

  // Update reserved quantity
  const { data, error } = await supabaseAdmin
    .from('inventory')
    .update({
      reserved_quantity: currentInventory.reserved_quantity + quantity,
      updated_at: new Date().toISOString()
    })
    .eq('product_id', productId)
    .select()
    .single()

  if (error) throw error
  return data
}

// Release reserved inventory
export const releaseReservedInventory = async (productId: string, quantity: number) => {
  const supabaseAdmin = createSupabaseAdmin()
  
  // Get current inventory
  const { data: currentInventory } = await supabaseAdmin
    .from('inventory')
    .select('reserved_quantity')
    .eq('product_id', productId)
    .single()

  if (!currentInventory) {
    throw new Error('Inventory record not found')
  }

  const newReservedQuantity = Math.max(0, currentInventory.reserved_quantity - quantity)

  // Update reserved quantity
  const { data, error } = await supabaseAdmin
    .from('inventory')
    .update({
      reserved_quantity: newReservedQuantity,
      updated_at: new Date().toISOString()
    })
    .eq('product_id', productId)
    .select()
    .single()

  if (error) throw error
  return data
}

// Fulfill order (reduce actual inventory and reserved)
export const fulfillInventory = async (productId: string, quantity: number) => {
  const supabaseAdmin = createSupabaseAdmin()
  
  // Get current inventory
  const { data: currentInventory } = await supabaseAdmin
    .from('inventory')
    .select('quantity, reserved_quantity')
    .eq('product_id', productId)
    .single()

  if (!currentInventory) {
    throw new Error('Inventory record not found')
  }

  const newQuantity = Math.max(0, currentInventory.quantity - quantity)
  const newReservedQuantity = Math.max(0, currentInventory.reserved_quantity - quantity)

  // Update inventory
  const { data, error } = await supabaseAdmin
    .from('inventory')
    .update({
      quantity: newQuantity,
      reserved_quantity: newReservedQuantity,
      updated_at: new Date().toISOString()
    })
    .eq('product_id', productId)
    .select()
    .single()

  if (error) throw error
  return data
}

// Get low stock products
export const getLowStockProducts = async (threshold?: number) => {
  const products = await getProducts({ lowStock: true })
  
  if (threshold) {
    return products.filter(product => (product.inventory?.quantity || 0) <= threshold)
  }
  
  return products
}

// Get out of stock products
export const getOutOfStockProducts = async () => {
  return getProducts({ outOfStock: true })
}

// Get inventory statistics
export const getInventoryStats = async () => {
  const products = await getProducts({ limit: 10000 })
  
  const totalProducts = products.length
  const outOfStock = products.filter(p => (p.inventory?.quantity || 0) === 0).length
  const lowStock = products.filter(p => {
    const qty = p.inventory?.quantity || 0
    const reorder = p.inventory?.reorder_level || 10
    return qty > 0 && qty <= reorder
  }).length
  const inStock = totalProducts - outOfStock - lowStock
  const totalValue = products.reduce((sum, p) => sum + (p.price * (p.inventory?.quantity || 0)), 0)
  const totalQuantity = products.reduce((sum, p) => sum + (p.inventory?.quantity || 0), 0)

  return {
    totalProducts,
    outOfStock,
    lowStock,
    inStock,
    totalValue,
    totalQuantity,
    averageValue: totalProducts > 0 ? totalValue / totalProducts : 0
  }
}
