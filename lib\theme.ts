export interface Theme {
  id: string
  name: string
  displayName: string
  description: string
  colors: {
    background: string
    foreground: string
    card: string
    cardForeground: string
    popover: string
    popoverForeground: string
    primary: string
    primaryForeground: string
    secondary: string
    secondaryForeground: string
    muted: string
    mutedForeground: string
    accent: string
    accentForeground: string
    destructive: string
    destructiveForeground: string
    border: string
    input: string
    ring: string
    chart1: string
    chart2: string
    chart3: string
    chart4: string
    chart5: string
    brand: string
    brandForeground: string
    brandSecondary: string
    brandMuted: string
    success: string
    successForeground: string
    warning: string
    warningForeground: string
    info: string
    infoForeground: string
    gradientFrom: string
    gradientVia: string
    gradientTo: string
    gradientBrandFrom: string
    gradientBrandVia: string
    gradientBrandTo: string
    surface: string
    surfaceForeground: string
    overlay: string
    overlayForeground: string
  }
}

export const themes: Theme[] = [
  {
    id: 'default',
    name: 'default',
    displayName: 'Vars<PERSON><PERSON>lan',
    description: '<PERSON><PERSON>ik açık tema',
    colors: {
      background: '0 0% 100%',
      foreground: '20 14.3% 4.1%',
      card: '0 0% 100%',
      cardForeground: '20 14.3% 4.1%',
      popover: '0 0% 100%',
      popoverForeground: '20 14.3% 4.1%',
      primary: '24 9.8% 10%',
      primaryForeground: '60 9.1% 97.8%',
      secondary: '60 4.8% 95.9%',
      secondaryForeground: '24 9.8% 10%',
      muted: '60 4.8% 95.9%',
      mutedForeground: '25 5.3% 44.7%',
      accent: '60 4.8% 95.9%',
      accentForeground: '24 9.8% 10%',
      destructive: '0 84.2% 60.2%',
      destructiveForeground: '60 9.1% 97.8%',
      border: '20 5.9% 90%',
      input: '20 5.9% 90%',
      ring: '20 14.3% 4.1%',
      chart1: '12 76% 61%',
      chart2: '173 58% 39%',
      chart3: '197 37% 24%',
      chart4: '43 74% 66%',
      chart5: '27 87% 67%',
      brand: '210 100% 50%',
      brandForeground: '0 0% 100%',
      brandSecondary: '210 80% 60%',
      brandMuted: '210 30% 90%',
      success: '120 60% 50%',
      successForeground: '0 0% 100%',
      warning: '45 100% 60%',
      warningForeground: '0 0% 0%',
      info: '210 100% 60%',
      infoForeground: '0 0% 100%',
      gradientFrom: '45 100% 70%',
      gradientVia: '30 100% 60%',
      gradientTo: '0 100% 60%',
      gradientBrandFrom: '270 70% 60%',
      gradientBrandVia: '210 100% 60%',
      gradientBrandTo: '260 100% 70%',
      surface: '0 0% 98%',
      surfaceForeground: '20 14.3% 4.1%',
      overlay: '0 0% 0%',
      overlayForeground: '0 0% 100%'
    }
  },
  {
    id: 'dark',
    name: 'dark',
    displayName: 'Karanlık',
    description: 'Koyu tema',
    colors: {
      background: '20 14.3% 4.1%',
      foreground: '60 9.1% 97.8%',
      card: '20 14.3% 4.1%',
      cardForeground: '60 9.1% 97.8%',
      popover: '20 14.3% 4.1%',
      popoverForeground: '60 9.1% 97.8%',
      primary: '60 9.1% 97.8%',
      primaryForeground: '24 9.8% 10%',
      secondary: '12 6.5% 15.1%',
      secondaryForeground: '60 9.1% 97.8%',
      muted: '12 6.5% 15.1%',
      mutedForeground: '24 5.4% 63.9%',
      accent: '12 6.5% 15.1%',
      accentForeground: '60 9.1% 97.8%',
      destructive: '0 62.8% 30.6%',
      destructiveForeground: '60 9.1% 97.8%',
      border: '12 6.5% 15.1%',
      input: '12 6.5% 15.1%',
      ring: '24 5.7% 82.9%',
      chart1: '220 70% 50%',
      chart2: '160 60% 45%',
      chart3: '30 80% 55%',
      chart4: '280 65% 60%',
      chart5: '340 75% 55%',
      brand: '210 100% 60%',
      brandForeground: '20 14.3% 4.1%',
      brandSecondary: '210 80% 70%',
      brandMuted: '210 20% 20%',
      success: '120 60% 60%',
      successForeground: '20 14.3% 4.1%',
      warning: '45 100% 70%',
      warningForeground: '20 14.3% 4.1%',
      info: '210 100% 70%',
      infoForeground: '20 14.3% 4.1%',
      gradientFrom: '45 100% 80%',
      gradientVia: '30 100% 70%',
      gradientTo: '0 100% 70%',
      gradientBrandFrom: '270 70% 70%',
      gradientBrandVia: '210 100% 70%',
      gradientBrandTo: '260 100% 80%',
      surface: '12 6.5% 15.1%',
      surfaceForeground: '60 9.1% 97.8%',
      overlay: '0 0% 0%',
      overlayForeground: '0 0% 100%'
    }
  },
  {
    id: 'blue-ocean',
    name: 'blue-ocean',
    displayName: 'Mavi Okyanus',
    description: 'Okyanus mavisi teması',
    colors: {
      background: '210 40% 98%',
      foreground: '210 40% 8%',
      card: '210 40% 98%',
      cardForeground: '210 40% 8%',
      popover: '210 40% 98%',
      popoverForeground: '210 40% 8%',
      primary: '210 100% 50%',
      primaryForeground: '210 40% 98%',
      secondary: '210 30% 90%',
      secondaryForeground: '210 40% 8%',
      muted: '210 30% 90%',
      mutedForeground: '210 20% 40%',
      accent: '210 30% 90%',
      accentForeground: '210 40% 8%',
      destructive: '0 84.2% 60.2%',
      destructiveForeground: '60 9.1% 97.8%',
      border: '210 30% 85%',
      input: '210 30% 85%',
      ring: '210 100% 50%',
      chart1: '210 100% 60%',
      chart2: '190 100% 50%',
      chart3: '230 100% 60%',
      chart4: '200 100% 55%',
      chart5: '220 100% 65%',
      brand: '210 100% 50%',
      brandForeground: '210 40% 98%',
      brandSecondary: '210 80% 60%',
      brandMuted: '210 30% 90%',
      success: '150 60% 50%',
      successForeground: '210 40% 98%',
      warning: '45 100% 60%',
      warningForeground: '210 40% 8%',
      info: '210 100% 60%',
      infoForeground: '210 40% 98%',
      gradientFrom: '200 100% 70%',
      gradientVia: '210 100% 60%',
      gradientTo: '220 100% 70%',
      gradientBrandFrom: '190 100% 60%',
      gradientBrandVia: '210 100% 50%',
      gradientBrandTo: '230 100% 60%',
      surface: '210 40% 96%',
      surfaceForeground: '210 40% 8%',
      overlay: '210 40% 8%',
      overlayForeground: '210 40% 98%'
    }
  },
  {
    id: 'green-forest',
    name: 'green-forest',
    displayName: 'Yeşil Orman',
    description: 'Doğa yeşili teması',
    colors: {
      background: '120 40% 98%',
      foreground: '120 40% 8%',
      card: '120 40% 98%',
      cardForeground: '120 40% 8%',
      popover: '120 40% 98%',
      popoverForeground: '120 40% 8%',
      primary: '120 60% 40%',
      primaryForeground: '120 40% 98%',
      secondary: '120 30% 90%',
      secondaryForeground: '120 40% 8%',
      muted: '120 30% 90%',
      mutedForeground: '120 20% 40%',
      accent: '120 30% 90%',
      accentForeground: '120 40% 8%',
      destructive: '0 84.2% 60.2%',
      destructiveForeground: '60 9.1% 97.8%',
      border: '120 30% 85%',
      input: '120 30% 85%',
      ring: '120 60% 40%',
      chart1: '120 60% 50%',
      chart2: '100 60% 45%',
      chart3: '140 60% 45%',
      chart4: '110 60% 50%',
      chart5: '130 60% 50%',
      brand: '120 60% 40%',
      brandForeground: '120 40% 98%',
      brandSecondary: '120 50% 50%',
      brandMuted: '120 30% 90%',
      success: '120 60% 50%',
      successForeground: '120 40% 98%',
      warning: '45 100% 60%',
      warningForeground: '120 40% 8%',
      info: '200 100% 60%',
      infoForeground: '120 40% 98%',
      gradientFrom: '100 60% 60%',
      gradientVia: '120 60% 50%',
      gradientTo: '140 60% 60%',
      gradientBrandFrom: '110 60% 50%',
      gradientBrandVia: '120 60% 40%',
      gradientBrandTo: '130 60% 50%',
      surface: '120 40% 96%',
      surfaceForeground: '120 40% 8%',
      overlay: '120 40% 8%',
      overlayForeground: '120 40% 98%'
    }
  },
  {
    id: 'purple-galaxy',
    name: 'purple-galaxy',
    displayName: 'Mor Galaksi',
    description: 'Galaksi moru teması',
    colors: {
      background: '270 40% 98%',
      foreground: '270 40% 8%',
      card: '270 40% 98%',
      cardForeground: '270 40% 8%',
      popover: '270 40% 98%',
      popoverForeground: '270 40% 8%',
      primary: '270 70% 50%',
      primaryForeground: '270 40% 98%',
      secondary: '270 30% 90%',
      secondaryForeground: '270 40% 8%',
      muted: '270 30% 90%',
      mutedForeground: '270 20% 40%',
      accent: '270 30% 90%',
      accentForeground: '270 40% 8%',
      destructive: '0 84.2% 60.2%',
      destructiveForeground: '60 9.1% 97.8%',
      border: '270 30% 85%',
      input: '270 30% 85%',
      ring: '270 70% 50%',
      chart1: '270 70% 60%',
      chart2: '250 70% 55%',
      chart3: '290 70% 55%',
      chart4: '260 70% 60%',
      chart5: '280 70% 60%',
      brand: '270 70% 50%',
      brandForeground: '270 40% 98%',
      brandSecondary: '270 60% 60%',
      brandMuted: '270 30% 90%',
      success: '120 60% 50%',
      successForeground: '270 40% 98%',
      warning: '45 100% 60%',
      warningForeground: '270 40% 8%',
      info: '210 100% 60%',
      infoForeground: '270 40% 98%',
      gradientFrom: '250 70% 60%',
      gradientVia: '270 70% 50%',
      gradientTo: '290 70% 60%',
      gradientBrandFrom: '260 70% 60%',
      gradientBrandVia: '270 70% 50%',
      gradientBrandTo: '280 70% 60%',
      surface: '270 40% 96%',
      surfaceForeground: '270 40% 8%',
      overlay: '270 40% 8%',
      overlayForeground: '270 40% 98%'
    }
  }
]

export const getThemeById = (id: string): Theme | undefined => {
  return themes.find(theme => theme.id === id)
}

export const getDefaultTheme = (): Theme => {
  return themes[0] // default theme
}
