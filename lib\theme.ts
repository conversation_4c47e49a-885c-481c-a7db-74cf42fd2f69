export interface Theme {
  id: string
  name: string
  displayName: string
  description: string
  colors: {
    background: string
    foreground: string
    card: string
    cardForeground: string
    popover: string
    popoverForeground: string
    primary: string
    primaryForeground: string
    secondary: string
    secondaryForeground: string
    muted: string
    mutedForeground: string
    accent: string
    accentForeground: string
    destructive: string
    destructiveForeground: string
    border: string
    input: string
    ring: string
    chart1: string
    chart2: string
    chart3: string
    chart4: string
    chart5: string
  }
}

export const themes: Theme[] = [
  {
    id: 'default',
    name: 'default',
    displayName: 'Vars<PERSON><PERSON><PERSON>',
    description: 'K<PERSON>ik açık tema',
    colors: {
      background: '0 0% 100%',
      foreground: '20 14.3% 4.1%',
      card: '0 0% 100%',
      cardForeground: '20 14.3% 4.1%',
      popover: '0 0% 100%',
      popoverForeground: '20 14.3% 4.1%',
      primary: '24 9.8% 10%',
      primaryForeground: '60 9.1% 97.8%',
      secondary: '60 4.8% 95.9%',
      secondaryForeground: '24 9.8% 10%',
      muted: '60 4.8% 95.9%',
      mutedForeground: '25 5.3% 44.7%',
      accent: '60 4.8% 95.9%',
      accentForeground: '24 9.8% 10%',
      destructive: '0 84.2% 60.2%',
      destructiveForeground: '60 9.1% 97.8%',
      border: '20 5.9% 90%',
      input: '20 5.9% 90%',
      ring: '20 14.3% 4.1%',
      chart1: '12 76% 61%',
      chart2: '173 58% 39%',
      chart3: '197 37% 24%',
      chart4: '43 74% 66%',
      chart5: '27 87% 67%'
    }
  },
  {
    id: 'dark',
    name: 'dark',
    displayName: 'Karanlık',
    description: 'Koyu tema',
    colors: {
      background: '20 14.3% 4.1%',
      foreground: '60 9.1% 97.8%',
      card: '20 14.3% 4.1%',
      cardForeground: '60 9.1% 97.8%',
      popover: '20 14.3% 4.1%',
      popoverForeground: '60 9.1% 97.8%',
      primary: '60 9.1% 97.8%',
      primaryForeground: '24 9.8% 10%',
      secondary: '12 6.5% 15.1%',
      secondaryForeground: '60 9.1% 97.8%',
      muted: '12 6.5% 15.1%',
      mutedForeground: '24 5.4% 63.9%',
      accent: '12 6.5% 15.1%',
      accentForeground: '60 9.1% 97.8%',
      destructive: '0 62.8% 30.6%',
      destructiveForeground: '60 9.1% 97.8%',
      border: '12 6.5% 15.1%',
      input: '12 6.5% 15.1%',
      ring: '24 5.7% 82.9%',
      chart1: '220 70% 50%',
      chart2: '160 60% 45%',
      chart3: '30 80% 55%',
      chart4: '280 65% 60%',
      chart5: '340 75% 55%'
    }
  },
  {
    id: 'blue-ocean',
    name: 'blue-ocean',
    displayName: 'Mavi Okyanus',
    description: 'Okyanus mavisi teması',
    colors: {
      background: '210 40% 98%',
      foreground: '210 40% 8%',
      card: '210 40% 98%',
      cardForeground: '210 40% 8%',
      popover: '210 40% 98%',
      popoverForeground: '210 40% 8%',
      primary: '210 100% 50%',
      primaryForeground: '210 40% 98%',
      secondary: '210 30% 90%',
      secondaryForeground: '210 40% 8%',
      muted: '210 30% 90%',
      mutedForeground: '210 20% 40%',
      accent: '210 30% 90%',
      accentForeground: '210 40% 8%',
      destructive: '0 84.2% 60.2%',
      destructiveForeground: '60 9.1% 97.8%',
      border: '210 30% 85%',
      input: '210 30% 85%',
      ring: '210 100% 50%',
      chart1: '210 100% 60%',
      chart2: '190 100% 50%',
      chart3: '230 100% 60%',
      chart4: '200 100% 55%',
      chart5: '220 100% 65%'
    }
  },
  {
    id: 'green-forest',
    name: 'green-forest',
    displayName: 'Yeşil Orman',
    description: 'Doğa yeşili teması',
    colors: {
      background: '120 40% 98%',
      foreground: '120 40% 8%',
      card: '120 40% 98%',
      cardForeground: '120 40% 8%',
      popover: '120 40% 98%',
      popoverForeground: '120 40% 8%',
      primary: '120 60% 40%',
      primaryForeground: '120 40% 98%',
      secondary: '120 30% 90%',
      secondaryForeground: '120 40% 8%',
      muted: '120 30% 90%',
      mutedForeground: '120 20% 40%',
      accent: '120 30% 90%',
      accentForeground: '120 40% 8%',
      destructive: '0 84.2% 60.2%',
      destructiveForeground: '60 9.1% 97.8%',
      border: '120 30% 85%',
      input: '120 30% 85%',
      ring: '120 60% 40%',
      chart1: '120 60% 50%',
      chart2: '100 60% 45%',
      chart3: '140 60% 45%',
      chart4: '110 60% 50%',
      chart5: '130 60% 50%'
    }
  },
  {
    id: 'purple-galaxy',
    name: 'purple-galaxy',
    displayName: 'Mor Galaksi',
    description: 'Galaksi moru teması',
    colors: {
      background: '270 40% 98%',
      foreground: '270 40% 8%',
      card: '270 40% 98%',
      cardForeground: '270 40% 8%',
      popover: '270 40% 98%',
      popoverForeground: '270 40% 8%',
      primary: '270 70% 50%',
      primaryForeground: '270 40% 98%',
      secondary: '270 30% 90%',
      secondaryForeground: '270 40% 8%',
      muted: '270 30% 90%',
      mutedForeground: '270 20% 40%',
      accent: '270 30% 90%',
      accentForeground: '270 40% 8%',
      destructive: '0 84.2% 60.2%',
      destructiveForeground: '60 9.1% 97.8%',
      border: '270 30% 85%',
      input: '270 30% 85%',
      ring: '270 70% 50%',
      chart1: '270 70% 60%',
      chart2: '250 70% 55%',
      chart3: '290 70% 55%',
      chart4: '260 70% 60%',
      chart5: '280 70% 60%'
    }
  }
]

export const getThemeById = (id: string): Theme | undefined => {
  return themes.find(theme => theme.id === id)
}

export const getDefaultTheme = (): Theme => {
  return themes[0] // default theme
}
