import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { Toaster } from 'react-hot-toast'
import { ConditionalLayout } from '@/components/layout/ConditionalLayout'
import { ThemeProvider } from '@/contexts/ThemeContext'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: '3dunyam - 3D Baskı Ürünleri E-Ticaret Platformu',
  description: 'Türkiye\'nin en büyük 3D baskı ürünleri e-ticaret platformu. 3D yazıcılar, filamentler, araçlar ve aksesuarlar.',
  keywords: '3d yazıcı, filament, 3d baskı, pla filament, abs filament, 3d printer',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="tr">
      <body className={inter.className}>
        <ThemeProvider>
          <ConditionalLayout>
            {children}
          </ConditionalLayout>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />
        </ThemeProvider>
      </body>
    </html>
  )
}