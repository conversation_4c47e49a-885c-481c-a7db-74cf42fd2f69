import { NextRequest, NextResponse } from 'next/server'
import { createOrder } from '@/lib/orders'
import { clearCart } from '@/lib/cart'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const orderData = await request.json()

    // Temporary: Skip authentication for testing
    // TODO: Re-enable authentication after fixing the issue

    // For now, use a default user ID for testing
    const defaultUserId = 'f3821fe6-68e1-43f8-9c7d-3e4162301ffb' // Replace with actual user ID

    // Validate order data
    if (!orderData.items || orderData.items.length === 0) {
      return NextResponse.json(
        { error: 'Order must contain at least one item' },
        { status: 400 }
      )
    }

    if (!orderData.shipping_address || !orderData.billing_address) {
      return NextResponse.json(
        { error: 'Shipping and billing addresses are required' },
        { status: 400 }
      )
    }

    // Ensure user_id matches authenticated user
    orderData.user_id = defaultUserId

    // Create order
    const order = await createOrder(orderData)

    return NextResponse.json({
      success: true,
      data: order,
      message: 'Order created successfully'
    })

  } catch (error) {
    console.error('Order creation API error:', error)
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Create Supabase client with cookies
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // This would typically get user's orders
    // For now, return empty array
    return NextResponse.json({
      success: true,
      data: [],
      message: 'Orders retrieved successfully'
    })

  } catch (error) {
    console.error('Orders API error:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve orders' },
      { status: 500 }
    )
  }
}
