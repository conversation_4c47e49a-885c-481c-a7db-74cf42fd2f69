'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Separator } from '@/components/ui/separator'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Checkbox } from '@/components/ui/checkbox'
import { getCart } from '@/lib/cart'
import { getCurrentUser } from '@/lib/auth'
// Orders will be created via API
import { toast } from 'react-hot-toast'
import {
  ShoppingCart,
  CreditCard,
  Truck,
  Shield,
  ArrowLeft,
  Check,
  Building
} from 'lucide-react'

export default function CheckoutPage() {
  const router = useRouter()
  const [cartItems, setCartItems] = useState<any[]>([])
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  
  // Form states
  const [shippingAddress, setShippingAddress] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
    country: 'Türkiye'
  })
  
  const [billingAddress, setBillingAddress] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
    country: 'Türkiye'
  })
  
  const [useSameAddress, setUseSameAddress] = useState(true)
  const [paymentMethod, setPaymentMethod] = useState('shopier')
  const [orderNotes, setOrderNotes] = useState('')
  const [shippingCost, setShippingCost] = useState(29.90)
  const [freeShippingThreshold, setFreeShippingThreshold] = useState(500)

  useEffect(() => {
    loadCheckoutData()
    loadShippingSettings()
  }, [])

  const loadShippingSettings = async () => {
    try {
      const settingsResponse = await fetch('/api/admin/settings/shipping')
      if (settingsResponse.ok) {
        const settingsData = await settingsResponse.json()
        setShippingCost(settingsData.data.shipping_cost.amount)
        setFreeShippingThreshold(settingsData.data.free_shipping_threshold.amount)
      }
    } catch (error) {
      console.error('Error loading shipping settings:', error)
    }
  }

  const loadCheckoutData = async () => {
    try {
      const userData = await getCurrentUser()
      if (!userData) {
        toast.error('Sipariş vermek için giriş yapmalısınız')
        router.push('/auth/login')
        return
      }

      setUser(userData)
      
      // Pre-fill user data
      setShippingAddress(prev => ({
        ...prev,
        firstName: userData.first_name || '',
        lastName: userData.last_name || '',
        email: userData.email || ''
      }))

      const items = await getCart(userData.id)
      if (items.length === 0) {
        toast.error('Sepetiniz boş')
        router.push('/cart')
        return
      }
      
      setCartItems(items)
    } catch (error) {
      console.error('Error loading checkout data:', error)
      toast.error('Checkout verisi yüklenemedi')
    } finally {
      setIsLoading(false)
    }
  }

  const calculateSubtotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0)
  }

  const calculateShipping = () => {
    const subtotal = calculateSubtotal()
    return subtotal >= freeShippingThreshold ? 0 : shippingCost
  }

  const calculateTotal = () => {
    return calculateSubtotal() + calculateShipping()
  }

  const handleSubmitOrder = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user) {
      toast.error('Kullanıcı bilgisi bulunamadı')
      return
    }

    setIsProcessing(true)
    
    try {
      // Validate required fields
      const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'postalCode']
      const missingFields = requiredFields.filter(field => !shippingAddress[field as keyof typeof shippingAddress])
      
      if (missingFields.length > 0) {
        toast.error('Lütfen tüm gerekli alanları doldurun')
        return
      }

      // Create order object
      const subtotal = calculateSubtotal()
      const shippingCost = calculateShipping()
      const total = calculateTotal()

      console.log('Order calculation:', { subtotal, shippingCost, total, cartItems })

      const orderData = {
        user_id: user.id,
        items: cartItems,
        shipping_address: shippingAddress,
        billing_address: useSameAddress ? shippingAddress : billingAddress,
        // payment_method: paymentMethod, // Skip for now due to schema mismatch
        subtotal,
        shipping_cost: shippingCost,
        total,
        notes: orderNotes
      }

      // Create order via API
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Sipariş oluşturulamadı')
      }

      const result = await response.json()
      const order = result.data

      // Handle different payment methods
      if (paymentMethod === 'shopier') {
        // Redirect to Shopier payment
        router.push(`/payment/shopier?order_id=${order.id}`)
      } else {
        // For other payment methods, clear cart and show success
        await fetch('/api/cart/clear', { method: 'POST' })
        toast.success('Siparişiniz başarıyla alındı!')
        router.push('/order-success')
      }
      
    } catch (error) {
      console.error('Error processing order:', error)
      toast.error('Sipariş işlenirken hata oluştu')
    } finally {
      setIsProcessing(false)
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-8">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-4">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-12 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
        <button onClick={() => router.back()} className="hover:text-gray-900">
          <ArrowLeft className="h-4 w-4" />
        </button>
        <span>/</span>
        <span className="text-gray-900">Sipariş Ver</span>
      </nav>

      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Sipariş Ver</h1>
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <Shield className="h-4 w-4" />
          <span>Güvenli Ödeme</span>
        </div>
      </div>

      <form onSubmit={handleSubmitOrder}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Forms */}
          <div className="space-y-8">
            {/* Shipping Address */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Truck className="mr-2 h-5 w-5" />
                  Teslimat Adresi
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">Ad *</Label>
                    <Input
                      id="firstName"
                      value={shippingAddress.firstName}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, firstName: e.target.value }))}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Soyad *</Label>
                    <Input
                      id="lastName"
                      value={shippingAddress.lastName}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, lastName: e.target.value }))}
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="email">E-posta *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={shippingAddress.email}
                    onChange={(e) => setShippingAddress(prev => ({ ...prev, email: e.target.value }))}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="phone">Telefon *</Label>
                  <Input
                    id="phone"
                    value={shippingAddress.phone}
                    onChange={(e) => setShippingAddress(prev => ({ ...prev, phone: e.target.value }))}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="address">Adres *</Label>
                  <Textarea
                    id="address"
                    value={shippingAddress.address}
                    onChange={(e) => setShippingAddress(prev => ({ ...prev, address: e.target.value }))}
                    required
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="city">Şehir *</Label>
                    <Input
                      id="city"
                      value={shippingAddress.city}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, city: e.target.value }))}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="postalCode">Posta Kodu *</Label>
                    <Input
                      id="postalCode"
                      value={shippingAddress.postalCode}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, postalCode: e.target.value }))}
                      required
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Billing Address */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="mr-2 h-5 w-5" />
                  Fatura Adresi
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2 mb-4">
                  <Checkbox
                    id="sameAddress"
                    checked={useSameAddress}
                    onCheckedChange={setUseSameAddress}
                  />
                  <Label htmlFor="sameAddress">Teslimat adresi ile aynı</Label>
                </div>
                
                {!useSameAddress && (
                  <div className="space-y-4">
                    {/* Same form fields as shipping address */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="billFirstName">Ad *</Label>
                        <Input
                          id="billFirstName"
                          value={billingAddress.firstName}
                          onChange={(e) => setBillingAddress(prev => ({ ...prev, firstName: e.target.value }))}
                          required={!useSameAddress}
                        />
                      </div>
                      <div>
                        <Label htmlFor="billLastName">Soyad *</Label>
                        <Input
                          id="billLastName"
                          value={billingAddress.lastName}
                          onChange={(e) => setBillingAddress(prev => ({ ...prev, lastName: e.target.value }))}
                          required={!useSameAddress}
                        />
                      </div>
                    </div>
                    {/* Add other billing address fields as needed */}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Payment Method */}
            <Card>
              <CardHeader>
                <CardTitle>Ödeme Yöntemi</CardTitle>
              </CardHeader>
              <CardContent>
                <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="shopier" id="shopier" />
                    <Label htmlFor="shopier" className="flex items-center space-x-2">
                      <CreditCard className="h-4 w-4" />
                      <span>Kredi Kartı (Shopier)</span>
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Güvenli</span>
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="bank-transfer" id="bank-transfer" />
                    <Label htmlFor="bank-transfer" className="flex items-center space-x-2">
                      <Building className="h-4 w-4" />
                      <span>Havale/EFT</span>
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="cash-on-delivery" id="cash-on-delivery" />
                    <Label htmlFor="cash-on-delivery" className="flex items-center space-x-2">
                      <Truck className="h-4 w-4" />
                      <span>Kapıda Ödeme</span>
                    </Label>
                  </div>
                </RadioGroup>
              </CardContent>
            </Card>

            {/* Order Notes */}
            <Card>
              <CardHeader>
                <CardTitle>Sipariş Notları</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  placeholder="Siparişiniz hakkında özel notlarınız..."
                  value={orderNotes}
                  onChange={(e) => setOrderNotes(e.target.value)}
                />
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle>Sipariş Özeti</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Cart Items */}
                <div className="space-y-3">
                  {cartItems.map((item) => {
                    const primaryImage = item.product.images?.find(img => img.is_primary) || item.product.images?.[0]
                    
                    return (
                      <div key={item.id} className="flex items-center space-x-3">
                        <div className="relative w-12 h-12 flex-shrink-0">
                          {primaryImage ? (
                            <Image
                              src={primaryImage.image_url}
                              alt={item.product.name}
                              fill
                              className="object-cover rounded"
                            />
                          ) : (
                            <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center">
                              <ShoppingCart className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {item.product.name}
                          </p>
                          <p className="text-sm text-gray-600">
                            {item.quantity} x {item.price.toLocaleString('tr-TR')} ₺
                          </p>
                        </div>
                        <div className="text-sm font-medium">
                          {(item.price * item.quantity).toLocaleString('tr-TR')} ₺
                        </div>
                      </div>
                    )
                  })}
                </div>

                <Separator />

                {/* Totals */}
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Ara Toplam:</span>
                    <span>{calculateSubtotal().toLocaleString('tr-TR')} ₺</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span>Kargo:</span>
                    <span>
                      {calculateShipping() === 0 ? (
                        <span className="text-green-600 font-medium">Ücretsiz</span>
                      ) : (
                        `${calculateShipping().toLocaleString('tr-TR')} ₺`
                      )}
                    </span>
                  </div>

                  <Separator />

                  <div className="flex justify-between text-lg font-semibold">
                    <span>Toplam:</span>
                    <span>{calculateTotal().toLocaleString('tr-TR')} ₺</span>
                  </div>
                </div>

                <Button 
                  type="submit" 
                  className="w-full" 
                  size="lg"
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    'İşleniyor...'
                  ) : (
                    <>
                      <Check className="mr-2 h-5 w-5" />
                      Siparişi Tamamla
                    </>
                  )}
                </Button>

                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <span>✓</span>
                    <span>256-bit SSL şifreleme</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span>✓</span>
                    <span>Güvenli ödeme</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span>✓</span>
                    <span>Kolay iade</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  )
}
