'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { getCategories } from '@/lib/products'
import { generateSlug } from '@/lib/csv-import'
import { toast } from 'react-hot-toast'
import { ArrowLeft, Save, Upload, X } from 'lucide-react'

export default function NewProductPage() {
  const router = useRouter()
  const [categories, setCategories] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  
  const [productData, setProductData] = useState({
    name: '',
    slug: '',
    description: '',
    short_description: '',
    price: '',
    compare_price: '',
    sku: '',
    category_id: '',
    material_type: 'PLA+',
    dimensions: '',
    weight_grams: '',
    is_active: true,
    is_featured: false,
    seo_title: '',
    seo_description: ''
  })

  const [images, setImages] = useState<string[]>([''])
  const [inventory, setInventory] = useState({
    quantity: '',
    reserved_quantity: '0',
    reorder_level: '10'
  })

  useEffect(() => {
    loadCategories()
  }, [])

  useEffect(() => {
    // Auto-generate slug from name
    if (productData.name && !productData.slug) {
      setProductData(prev => ({
        ...prev,
        slug: generateSlug(productData.name)
      }))
    }
  }, [productData.name])

  const loadCategories = async () => {
    setIsLoading(true)
    try {
      const categoriesData = await getCategories()
      setCategories(categoriesData)
    } catch (error) {
      console.error('Error loading categories:', error)
      toast.error('Kategoriler yüklenemedi')
    } finally {
      setIsLoading(false)
    }
  }

  const handleImageChange = (index: number, value: string) => {
    const newImages = [...images]
    newImages[index] = value
    setImages(newImages)
  }

  const addImageField = () => {
    setImages([...images, ''])
  }

  const removeImageField = (index: number) => {
    if (images.length > 1) {
      const newImages = images.filter((_, i) => i !== index)
      setImages(newImages)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validation
    if (!productData.name || !productData.sku || !productData.price) {
      toast.error('Lütfen gerekli alanları doldurun')
      return
    }

    if (!productData.category_id) {
      toast.error('Lütfen bir kategori seçin')
      return
    }

    setIsSaving(true)

    try {
      // Prepare product data
      const validImages = images.filter(img => img.trim())

      const newProductData = {
        name: productData.name,
        slug: productData.slug || generateSlug(productData.name),
        description: productData.description,
        short_description: productData.short_description,
        price: parseFloat(productData.price),
        compare_price: productData.compare_price ? parseFloat(productData.compare_price) : null,
        sku: productData.sku,
        category_id: productData.category_id,
        material_type: productData.material_type,
        dimensions: productData.dimensions || null,
        weight_grams: productData.weight_grams ? parseInt(productData.weight_grams) : null,
        is_active: productData.is_active,
        is_featured: productData.is_featured,
        seo_title: productData.seo_title || productData.name,
        seo_description: productData.seo_description || productData.short_description,
        images: validImages.map((url, index) => ({
          image_url: url,
          alt_text: productData.name,
          sort_order: index,
          is_primary: index === 0
        })),
        inventory: {
          quantity: parseInt(inventory.quantity) || 0,
          reserved_quantity: parseInt(inventory.reserved_quantity) || 0,
          reorder_level: parseInt(inventory.reorder_level) || 10
        }
      }

      // Create product via API
      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newProductData)
      })

      if (response.ok) {
        toast.success('Ürün başarıyla oluşturuldu')
        router.push('/admin/products')
      } else {
        const error = await response.json()
        throw new Error(error.message || 'Ürün oluşturulamadı')
      }

    } catch (error) {
      console.error('Error creating product:', error)
      toast.error('Ürün oluşturulamadı')
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Geri
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Yeni Ürün Ekle</h1>
            <p className="text-gray-600 mt-2">Yeni bir ürün oluşturun ve sisteme ekleyin</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Temel Bilgiler</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="name">Ürün Adı *</Label>
                  <Input
                    id="name"
                    value={productData.name}
                    onChange={(e) => setProductData(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="slug">URL Slug</Label>
                  <Input
                    id="slug"
                    value={productData.slug}
                    onChange={(e) => setProductData(prev => ({ ...prev, slug: e.target.value }))}
                    placeholder="otomatik-olusturulur"
                  />
                </div>

                <div>
                  <Label htmlFor="shortDescription">Kısa Açıklama</Label>
                  <Textarea
                    id="shortDescription"
                    value={productData.short_description}
                    onChange={(e) => setProductData(prev => ({ ...prev, short_description: e.target.value }))}
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="description">Detaylı Açıklama</Label>
                  <Textarea
                    id="description"
                    value={productData.description}
                    onChange={(e) => setProductData(prev => ({ ...prev, description: e.target.value }))}
                    rows={6}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Pricing */}
            <Card>
              <CardHeader>
                <CardTitle>Fiyatlandırma</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="price">Satış Fiyatı (₺) *</Label>
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      value={productData.price}
                      onChange={(e) => setProductData(prev => ({ ...prev, price: e.target.value }))}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="comparePrice">Karşılaştırma Fiyatı (₺)</Label>
                    <Input
                      id="comparePrice"
                      type="number"
                      step="0.01"
                      value={productData.compare_price}
                      onChange={(e) => setProductData(prev => ({ ...prev, compare_price: e.target.value }))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Product Images */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Ürün Görselleri
                  <Button type="button" variant="outline" size="sm" onClick={addImageField}>
                    <Upload className="mr-2 h-4 w-4" />
                    Görsel Ekle
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {images.map((image, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="flex-1">
                      <Label htmlFor={`image-${index}`}>
                        Görsel URL {index + 1} {index === 0 && '(Ana Görsel)'}
                      </Label>
                      <Input
                        id={`image-${index}`}
                        value={image}
                        onChange={(e) => handleImageChange(index, e.target.value)}
                        placeholder="https://example.com/image.jpg"
                      />
                    </div>
                    {images.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => removeImageField(index)}
                        className="mt-6"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Product Details */}
            <Card>
              <CardHeader>
                <CardTitle>Ürün Detayları</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="sku">SKU *</Label>
                  <Input
                    id="sku"
                    value={productData.sku}
                    onChange={(e) => setProductData(prev => ({ ...prev, sku: e.target.value }))}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="category">Kategori *</Label>
                  <Select
                    value={productData.category_id}
                    onValueChange={(value) => setProductData(prev => ({ ...prev, category_id: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Kategori seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="material">Malzeme Türü</Label>
                  <Select
                    value={productData.material_type}
                    onValueChange={(value) => setProductData(prev => ({ ...prev, material_type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PLA">PLA</SelectItem>
                      <SelectItem value="PLA+">PLA+</SelectItem>
                      <SelectItem value="ABS">ABS</SelectItem>
                      <SelectItem value="PETG">PETG</SelectItem>
                      <SelectItem value="TPU">TPU</SelectItem>
                      <SelectItem value="Wood">Wood</SelectItem>
                      <SelectItem value="Metal">Metal</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="dimensions">Boyutlar</Label>
                  <Input
                    id="dimensions"
                    value={productData.dimensions}
                    onChange={(e) => setProductData(prev => ({ ...prev, dimensions: e.target.value }))}
                    placeholder="20x15x10 cm"
                  />
                </div>

                <div>
                  <Label htmlFor="weight">Ağırlık (gram)</Label>
                  <Input
                    id="weight"
                    type="number"
                    value={productData.weight_grams}
                    onChange={(e) => setProductData(prev => ({ ...prev, weight_grams: e.target.value }))}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Inventory */}
            <Card>
              <CardHeader>
                <CardTitle>Stok Bilgileri</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="quantity">Stok Miktarı</Label>
                  <Input
                    id="quantity"
                    type="number"
                    value={inventory.quantity}
                    onChange={(e) => setInventory(prev => ({ ...prev, quantity: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="reorderLevel">Yeniden Sipariş Seviyesi</Label>
                  <Input
                    id="reorderLevel"
                    type="number"
                    value={inventory.reorder_level}
                    onChange={(e) => setInventory(prev => ({ ...prev, reorder_level: e.target.value }))}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Status */}
            <Card>
              <CardHeader>
                <CardTitle>Durum</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isActive"
                    checked={productData.is_active}
                    onCheckedChange={(checked) => setProductData(prev => ({ ...prev, is_active: !!checked }))}
                  />
                  <Label htmlFor="isActive">Aktif</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isFeatured"
                    checked={productData.is_featured}
                    onCheckedChange={(checked) => setProductData(prev => ({ ...prev, is_featured: !!checked }))}
                  />
                  <Label htmlFor="isFeatured">Öne Çıkan</Label>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <Button type="submit" className="w-full" disabled={isSaving}>
                    <Save className="mr-2 h-4 w-4" />
                    {isSaving ? 'Kaydediliyor...' : 'Ürünü Kaydet'}
                  </Button>
                  
                  <Button type="button" variant="outline" className="w-full" onClick={() => router.back()}>
                    İptal
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  )
}
