'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { getCurrentUser } from '@/lib/auth'
import { User } from '@/lib/auth'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import VerticalTabs from '@/components/ui/vertical-tabs'
import {
  LayoutDashboard,
  Package,
  Upload,
  Users,
  ShoppingCart,
  Settings,
  LogOut,
  Warehouse
} from 'lucide-react'

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const currentUser = await getCurrentUser()
      if (!currentUser || currentUser.role !== 'admin') {
        router.push('/auth/login')
        return
      }
      setUser(currentUser)
    } catch (error) {
      router.push('/auth/login')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user || user.role !== 'admin') {
    return null
  }

  // Aktif tab'ı pathname'den belirle
  const getActiveTab = () => {
    if (pathname === '/admin') return 'dashboard'
    if (pathname.startsWith('/admin/products')) return 'products'
    if (pathname.startsWith('/admin/inventory')) return 'inventory'
    if (pathname.startsWith('/admin/import/direct')) return 'import-direct'
    if (pathname.startsWith('/admin/import')) return 'import'
    if (pathname.startsWith('/admin/orders')) return 'orders'
    if (pathname.startsWith('/admin/users')) return 'users'
    if (pathname.startsWith('/admin/settings')) return 'settings'
    return 'dashboard'
  }

  const tabs = [
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: LayoutDashboard,
      badge: 3,
      content: children,
      onClick: () => router.push('/admin')
    },
    {
      id: 'products',
      title: 'Ürünler',
      icon: Package,
      content: children,
      onClick: () => router.push('/admin/products')
    },
    {
      id: 'inventory',
      title: 'Stok Yönetimi',
      icon: Warehouse,
      content: children,
      onClick: () => router.push('/admin/inventory')
    },
    {
      id: 'import',
      title: 'CSV İçe Aktar',
      icon: Upload,
      content: children,
      onClick: () => router.push('/admin/import')
    },
    {
      id: 'import-direct',
      title: 'Doğrudan İçe Aktar',
      icon: Upload,
      content: children,
      onClick: () => router.push('/admin/import/direct')
    },
    {
      id: 'orders',
      title: 'Siparişler',
      icon: ShoppingCart,
      badge: 12,
      content: children,
      onClick: () => router.push('/admin/orders')
    },
    {
      id: 'users',
      title: 'Kullanıcılar',
      icon: Users,
      content: children,
      onClick: () => router.push('/admin/users')
    },
    {
      id: 'settings',
      title: 'Ayarlar',
      icon: Settings,
      content: children,
      onClick: () => router.push('/admin/settings')
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 px-6 py-4">
        <div className="flex items-center justify-between">
          <Link href="/admin" className="text-xl font-bold text-blue-600 dark:text-blue-400">
            3Dünyam Admin
          </Link>

          <div className="flex items-center gap-4">
            <div className="text-right">
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {user.first_name} {user.last_name}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">{user.email}</p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/auth/logout')}
              className="text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
            >
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Vertical Tabs */}
      <div className="p-6">
        <VerticalTabs
          tabs={tabs}
          defaultTab={getActiveTab()}
          className="h-[calc(100vh-140px)] max-w-none"
        />
      </div>
    </div>
  )
}
