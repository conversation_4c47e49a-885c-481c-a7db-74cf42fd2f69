import { NextRequest, NextResponse } from 'next/server'
import { deleteProduct, updateProduct, getProductById, updateProductImages, updateProductInventory } from '@/lib/products'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const productId = id

    const product = await getProductById(productId)

    return NextResponse.json({
      success: true,
      data: product
    })
  } catch (error) {
    console.error('Product fetch API error:', error)
    return NextResponse.json(
      { error: '<PERSON><PERSON><PERSON>n bulunamadı' },
      { status: 404 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const productId = id
    const requestData = await request.json()

    // Extract product data (excluding images and inventory)
    const { images, inventory, ...productData } = requestData

    // Update product basic information
    const updatedProduct = await updateProduct(productId, productData)

    // Update images if provided
    if (images && Array.isArray(images)) {
      const validImages = images.filter(img => img.trim() !== '')
      const imageData = validImages.map((url: string, index: number) => ({
        image_url: url,
        alt_text: productData.name || 'Product Image',
        sort_order: index,
        is_primary: index === 0
      }))

      await updateProductImages(productId, imageData)
    }

    // Update inventory if provided
    if (inventory) {
      await updateProductInventory(productId, {
        quantity: parseInt(inventory.quantity) || 0,
        reserved_quantity: parseInt(inventory.reserved_quantity) || 0,
        reorder_level: parseInt(inventory.reorder_level) || 10
      })
    }

    return NextResponse.json({
      success: true,
      data: updatedProduct,
      message: 'Ürün başarıyla güncellendi'
    })
  } catch (error) {
    console.error('Product update API error:', error)
    return NextResponse.json(
      { error: 'Ürün güncellenemedi' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const productId = id

    const result = await deleteProduct(productId)

    return NextResponse.json({
      success: true,
      message: result.message,
      type: result.type
    })
  } catch (error) {
    console.error('Product deletion API error:', error)
    return NextResponse.json(
      { error: 'Ürün silinemedi' },
      { status: 500 }
    )
  }
}
