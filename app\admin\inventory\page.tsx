'use client'

import { useEffect, useState } from 'react'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
// import { getProducts } from '@/lib/inventory' // Moved to API route
import { toast } from 'react-hot-toast'
import { 
  Package, 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown,
  Edit,
  Search,
  Download,
  RefreshCw
} from 'lucide-react'

export default function InventoryPage() {
  const [products, setProducts] = useState<any[]>([])
  const [filteredProducts, setFilteredProducts] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProduct, setSelectedProduct] = useState<any>(null)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  
  const [updateData, setUpdateData] = useState({
    quantity: '',
    reserved_quantity: '',
    reorder_level: '',
    notes: ''
  })

  useEffect(() => {
    loadInventory()
  }, [])

  useEffect(() => {
    // Filter products based on search query
    const filtered = products.filter(product =>
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchQuery.toLowerCase())
    )
    setFilteredProducts(filtered)
  }, [products, searchQuery])

  const loadInventory = async () => {
    try {
      const response = await fetch('/api/admin/inventory')
      if (!response.ok) {
        throw new Error('Failed to fetch inventory')
      }
      const productsData = await response.json()
      setProducts(productsData)
    } catch (error) {
      console.error('Error loading inventory:', error)
      toast.error('Stok bilgileri yüklenemedi')
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdateStock = (product: any) => {
    setSelectedProduct(product)
    setUpdateData({
      quantity: product.inventory?.quantity?.toString() || '0',
      reserved_quantity: product.inventory?.reserved_quantity?.toString() || '0',
      reorder_level: product.inventory?.reorder_level?.toString() || '10',
      notes: ''
    })
    setIsUpdateDialogOpen(true)
  }

  const handleSaveUpdate = async () => {
    if (!selectedProduct) return

    setIsUpdating(true)
    try {
      const response = await fetch(`/api/admin/inventory/${selectedProduct.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          quantity: parseInt(updateData.quantity) || 0,
          reserved_quantity: parseInt(updateData.reserved_quantity) || 0,
          reorder_level: parseInt(updateData.reorder_level) || 10
        })
      })

      if (response.ok) {
        toast.success('Stok bilgileri güncellendi')
        setIsUpdateDialogOpen(false)
        await loadInventory() // Reload data
      } else {
        throw new Error('Update failed')
      }
    } catch (error) {
      console.error('Error updating inventory:', error)
      toast.error('Stok güncellenemedi')
    } finally {
      setIsUpdating(false)
    }
  }

  const getStockStatus = (product: any) => {
    const quantity = product.inventory?.quantity || 0
    const reorderLevel = product.inventory?.reorder_level || 10
    
    if (quantity === 0) {
      return { 
        label: 'Stokta Yok', 
        color: 'bg-red-100 text-red-800',
        icon: AlertTriangle,
        priority: 3
      }
    } else if (quantity <= reorderLevel) {
      return { 
        label: 'Düşük Stok', 
        color: 'bg-yellow-100 text-yellow-800',
        icon: TrendingDown,
        priority: 2
      }
    } else {
      return { 
        label: 'Stokta Var', 
        color: 'bg-green-100 text-green-800',
        icon: TrendingUp,
        priority: 1
      }
    }
  }

  const getInventoryStats = () => {
    const totalProducts = products.length
    const outOfStock = products.filter(p => (p.inventory?.quantity || 0) === 0).length
    const lowStock = products.filter(p => {
      const qty = p.inventory?.quantity || 0
      const reorder = p.inventory?.reorder_level || 10
      return qty > 0 && qty <= reorder
    }).length
    const inStock = totalProducts - outOfStock - lowStock
    const totalValue = products.reduce((sum, p) => sum + (p.price * (p.inventory?.quantity || 0)), 0)

    return { totalProducts, outOfStock, lowStock, inStock, totalValue }
  }

  const stats = getInventoryStats()

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Stok Yönetimi</h1>
          <p className="text-gray-600 mt-2">
            Ürün stoklarını takip edin ve yönetin
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadInventory}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Yenile
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Rapor İndir
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Toplam Ürün</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalProducts}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Stokta Var</p>
                <p className="text-2xl font-bold text-green-600">{stats.inStock}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Düşük Stok</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.lowStock}</p>
              </div>
              <TrendingDown className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Stokta Yok</p>
                <p className="text-2xl font-bold text-red-600">{stats.outOfStock}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Stok Değeri</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.totalValue.toLocaleString('tr-TR')} ₺
                </p>
              </div>
              <Package className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Ürün adı veya SKU ile ara..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Inventory Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            Stok Durumu ({filteredProducts.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">Resim</TableHead>
                  <TableHead>Ürün</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Mevcut Stok</TableHead>
                  <TableHead>Rezerve</TableHead>
                  <TableHead>Yeniden Sipariş</TableHead>
                  <TableHead>Durum</TableHead>
                  <TableHead>Stok Değeri</TableHead>
                  <TableHead className="w-16">İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts
                  .sort((a, b) => getStockStatus(b).priority - getStockStatus(a).priority)
                  .map((product) => {
                    const primaryImage = product.images?.find((img: any) => img.is_primary) || product.images?.[0]
                    const stockStatus = getStockStatus(product)
                    const inventory = product.inventory || {}
                    const stockValue = product.price * (inventory.quantity || 0)
                    
                    return (
                      <TableRow key={product.id}>
                        <TableCell>
                          <div className="relative w-12 h-12">
                            {primaryImage ? (
                              <Image
                                src={primaryImage.image_url}
                                alt={product.name}
                                fill
                                className="object-cover rounded"
                              />
                            ) : (
                              <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center">
                                <Package className="h-6 w-6 text-gray-400" />
                              </div>
                            )}
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <div>
                            <p className="font-medium text-gray-900 truncate max-w-xs">
                              {product.name}
                            </p>
                            <p className="text-sm text-gray-600">
                              {product.price.toLocaleString('tr-TR')} ₺
                            </p>
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                            {product.sku}
                          </code>
                        </TableCell>
                        
                        <TableCell>
                          <span className="font-medium text-lg">
                            {inventory.quantity || 0}
                          </span>
                        </TableCell>
                        
                        <TableCell>
                          <span className="text-gray-600">
                            {inventory.reserved_quantity || 0}
                          </span>
                        </TableCell>
                        
                        <TableCell>
                          <span className="text-gray-600">
                            {inventory.reorder_level || 10}
                          </span>
                        </TableCell>
                        
                        <TableCell>
                          <Badge className={stockStatus.color}>
                            <stockStatus.icon className="mr-1 h-3 w-3" />
                            {stockStatus.label}
                          </Badge>
                        </TableCell>
                        
                        <TableCell>
                          <span className="font-medium">
                            {stockValue.toLocaleString('tr-TR')} ₺
                          </span>
                        </TableCell>
                        
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleUpdateStock(product)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    )
                  })}
              </TableBody>
            </Table>
          </div>
          
          {filteredProducts.length === 0 && (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchQuery ? 'Ürün Bulunamadı' : 'Henüz Ürün Yok'}
              </h3>
              <p className="text-gray-600">
                {searchQuery 
                  ? 'Arama kriterlerinize uygun ürün bulunamadı.'
                  : 'Stok takibi için ürün ekleyin.'
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Update Stock Dialog */}
      <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Stok Güncelle</DialogTitle>
            <DialogDescription>
              {selectedProduct?.name} için stok bilgilerini güncelleyin
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="quantity">Mevcut Stok</Label>
              <Input
                id="quantity"
                type="number"
                value={updateData.quantity}
                onChange={(e) => setUpdateData(prev => ({ ...prev, quantity: e.target.value }))}
              />
            </div>
            
            <div>
              <Label htmlFor="reserved">Rezerve Stok</Label>
              <Input
                id="reserved"
                type="number"
                value={updateData.reserved_quantity}
                onChange={(e) => setUpdateData(prev => ({ ...prev, reserved_quantity: e.target.value }))}
              />
            </div>
            
            <div>
              <Label htmlFor="reorder">Yeniden Sipariş Seviyesi</Label>
              <Input
                id="reorder"
                type="number"
                value={updateData.reorder_level}
                onChange={(e) => setUpdateData(prev => ({ ...prev, reorder_level: e.target.value }))}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsUpdateDialogOpen(false)}>
              İptal
            </Button>
            <Button onClick={handleSaveUpdate} disabled={isUpdating}>
              {isUpdating ? 'Güncelleniyor...' : 'Güncelle'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
