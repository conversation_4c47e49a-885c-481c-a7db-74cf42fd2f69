import { NextRequest, NextResponse } from 'next/server'
import { clearCart } from '@/lib/cart'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    // Temporary: Skip authentication for testing
    const defaultUserId = 'f3821fe6-68e1-43f8-9c7d-3e4162301ffb'

    // Clear user's cart
    await clearCart(defaultUserId)

    return NextResponse.json({
      success: true,
      message: 'Cart cleared successfully'
    })

  } catch (error) {
    console.error('Clear cart API error:', error)
    return NextResponse.json(
      { error: 'Failed to clear cart' },
      { status: 500 }
    )
  }
}
