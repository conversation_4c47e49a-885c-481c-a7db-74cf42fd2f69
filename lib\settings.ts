import { supabase, createSupabaseAdmin } from './supabase'

export interface SiteSetting {
  id: string
  key: string
  value: any
  description: string
  category: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface ShippingSettings {
  shipping_cost: {
    amount: number
    currency: string
  }
  free_shipping_threshold: {
    amount: number
    currency: string
  }
  shipping_enabled: boolean
}

// Default settings when table doesn't exist
function getDefaultSettings(): SiteSetting[] {
  return [
    {
      id: 'default-1',
      key: 'shipping_cost',
      value: { amount: 29.90, currency: 'TRY' },
      description: 'Default shipping cost',
      category: 'shipping',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'default-2',
      key: 'free_shipping_threshold',
      value: { amount: 500, currency: 'TRY' },
      description: 'Minimum order amount for free shipping',
      category: 'shipping',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'default-3',
      key: 'shipping_enabled',
      value: true,
      description: 'Enable/disable shipping',
      category: 'shipping',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'default-4',
      key: 'site_name',
      value: '3Dünyam',
      description: 'Site name',
      category: 'general',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'default-5',
      key: 'site_description',
      value: '3D Baskı Ürünleri E-Ticaret Platformu',
      description: 'Site description',
      category: 'general',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'default-6',
      key: 'contact_email',
      value: '<EMAIL>',
      description: 'Contact email address',
      category: 'general',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'default-7',
      key: 'contact_phone',
      value: '+90 ************',
      description: 'Contact phone number',
      category: 'general',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ]
}

// Get all settings
export async function getAllSettings(): Promise<SiteSetting[]> {
  try {
    const supabaseAdmin = createSupabaseAdmin()

    const { data, error } = await supabaseAdmin
      .from('site_settings')
      .select('*')
      .eq('is_active', true)
      .order('category', { ascending: true })
      .order('key', { ascending: true })

    if (error) {
      console.error('Error fetching settings:', error)
      // Return default settings if table doesn't exist
      return getDefaultSettings()
    }

    return data || []
  } catch (error) {
    console.error('Error fetching settings:', error)
    return getDefaultSettings()
  }
}

// Get settings by category
export async function getSettingsByCategory(category: string): Promise<SiteSetting[]> {
  try {
    const supabaseAdmin = createSupabaseAdmin()

    const { data, error } = await supabaseAdmin
      .from('site_settings')
      .select('*')
      .eq('category', category)
      .eq('is_active', true)
      .order('key', { ascending: true })

    if (error) {
      console.error('Error fetching settings by category:', error)
      // Return default settings filtered by category
      return getDefaultSettings().filter(setting => setting.category === category)
    }

    return data || []
  } catch (error) {
    console.error('Error fetching settings by category:', error)
    return getDefaultSettings().filter(setting => setting.category === category)
  }
}

// Get a specific setting by key
export async function getSetting(key: string): Promise<any> {
  try {
    const supabaseAdmin = createSupabaseAdmin()

    const { data, error } = await supabaseAdmin
      .from('site_settings')
      .select('value')
      .eq('key', key)
      .eq('is_active', true)
      .single()

    if (error) {
      console.error('Error fetching setting:', error)
      // Return default value for the key
      const defaultSetting = getDefaultSettings().find(setting => setting.key === key)
      return defaultSetting?.value || null
    }

    return data?.value || null
  } catch (error) {
    console.error('Error fetching setting:', error)
    const defaultSetting = getDefaultSettings().find(setting => setting.key === key)
    return defaultSetting?.value || null
  }
}

// Update a setting
export async function updateSetting(key: string, value: any, description?: string): Promise<void> {
  try {
    const supabaseAdmin = createSupabaseAdmin()

    const updateData: any = {
      value,
      updated_at: new Date().toISOString()
    }

    if (description) {
      updateData.description = description
    }

    const { error } = await supabaseAdmin
      .from('site_settings')
      .update(updateData)
      .eq('key', key)

    if (error) {
      console.error('Error updating setting:', error)
      // For now, just log the error - in production you might want to create the table
      console.warn(`Setting ${key} could not be updated - table may not exist`)
    }
  } catch (error) {
    console.error('Error updating setting:', error)
    console.warn(`Setting ${key} could not be updated - table may not exist`)
  }
}

// Create a new setting
export async function createSetting(
  key: string, 
  value: any, 
  description: string = '', 
  category: string = 'general'
): Promise<void> {
  const supabaseAdmin = createSupabaseAdmin()
  
  const { error } = await supabaseAdmin
    .from('site_settings')
    .insert({
      key,
      value,
      description,
      category
    })

  if (error) {
    console.error('Error creating setting:', error)
    throw new Error('Ayar oluşturulamadı')
  }
}

// Get shipping settings
export async function getShippingSettings(): Promise<ShippingSettings> {
  const settings = await getSettingsByCategory('shipping')
  
  const shippingSettings: ShippingSettings = {
    shipping_cost: { amount: 29.90, currency: 'TRY' },
    free_shipping_threshold: { amount: 500, currency: 'TRY' },
    shipping_enabled: true
  }

  settings.forEach(setting => {
    if (setting.key === 'shipping_cost') {
      shippingSettings.shipping_cost = setting.value
    } else if (setting.key === 'free_shipping_threshold') {
      shippingSettings.free_shipping_threshold = setting.value
    } else if (setting.key === 'shipping_enabled') {
      shippingSettings.shipping_enabled = setting.value
    }
  })

  return shippingSettings
}

// Update shipping settings
export async function updateShippingSettings(settings: Partial<ShippingSettings>): Promise<void> {
  const promises = []

  if (settings.shipping_cost) {
    promises.push(updateSetting('shipping_cost', settings.shipping_cost, 'Default shipping cost'))
  }

  if (settings.free_shipping_threshold) {
    promises.push(updateSetting('free_shipping_threshold', settings.free_shipping_threshold, 'Minimum order amount for free shipping'))
  }

  if (settings.shipping_enabled !== undefined) {
    promises.push(updateSetting('shipping_enabled', settings.shipping_enabled, 'Enable/disable shipping'))
  }

  await Promise.all(promises)
}

// Calculate shipping cost based on current settings
export async function calculateShippingCost(subtotal: number): Promise<number> {
  const shippingSettings = await getShippingSettings()
  
  if (!shippingSettings.shipping_enabled) {
    return 0
  }

  if (subtotal >= shippingSettings.free_shipping_threshold.amount) {
    return 0
  }

  return shippingSettings.shipping_cost.amount
}
