# Deployment Guide

## 1. Overview

This guide covers the deployment process for the 3Dünyam e-commerce platform. The application is designed to be deployed on Vercel with Supabase as the backend service.

### 1.1 Architecture Overview
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Vercel      │    │    Supabase     │    │      CDN        │
│   (Frontend +   │◄──►│   (Database +   │    │   (Static       │
│   API Routes)   │    │   Auth + APIs)  │    │    Assets)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 Technology Stack
- **Frontend Hosting**: Vercel
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **File Storage**: Supabase Storage
- **CDN**: Vercel Edge Network
- **Domain**: Custom domain via Vercel

## 2. Prerequisites

### 2.1 Required Accounts
- **Vercel Account**: For hosting the application
- **Supabase Account**: For database and backend services
- **GitHub Account**: For code repository and CI/CD
- **Domain Provider**: For custom domain (optional)

### 2.2 Required Tools
- **Git**: Version control
- **Node.js**: v18.0.0 or higher
- **Bun**: Package manager
- **Vercel CLI**: For deployment management
- **Supabase CLI**: For database management

### 2.3 Installation
```bash
# Install Vercel CLI
npm i -g vercel

# Install Supabase CLI
npm i -g supabase

# Verify installations
vercel --version
supabase --version
```

## 3. Environment Setup

### 3.1 Development Environment
```env
# .env.local (development)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NODE_ENV=development
```

### 3.2 Production Environment
```env
# Production environment variables (set in Vercel)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
NEXT_PUBLIC_SITE_URL=https://your-domain.com
NODE_ENV=production
```

### 3.3 Environment Variable Security
- **Public variables**: Prefixed with `NEXT_PUBLIC_`
- **Private variables**: Server-side only (no prefix)
- **Sensitive data**: Never commit to repository
- **Key rotation**: Regular rotation of API keys

## 4. Supabase Setup

### 4.1 Database Setup

1. **Create Supabase Project**
```bash
# Login to Supabase
supabase login

# Initialize project
supabase init

# Link to remote project
supabase link --project-ref your-project-ref
```

2. **Run Migrations**
```bash
# Apply database migrations
supabase db push

# Verify migration status
supabase migration list
```

3. **Set up RLS Policies**
```sql
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
-- ... (other tables)

-- Apply security policies
-- (See DATABASE_SCHEMA.md for complete policies)
```

### 4.2 Authentication Setup

1. **Configure Auth Providers**
```sql
-- In Supabase Dashboard > Authentication > Settings
-- Enable Email/Password authentication
-- Configure email templates
-- Set up redirect URLs
```

2. **Email Templates**
```html
<!-- Confirmation email template -->
<h2>Confirm your signup</h2>
<p>Follow this link to confirm your user:</p>
<p><a href="{{ .ConfirmationURL }}">Confirm your mail</a></p>
```

### 4.3 Storage Setup
```sql
-- Create storage bucket for product images
INSERT INTO storage.buckets (id, name, public) 
VALUES ('product-images', 'product-images', true);

-- Set up storage policies
CREATE POLICY "Public read access" ON storage.objects
FOR SELECT USING (bucket_id = 'product-images');

CREATE POLICY "Admin upload access" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'product-images' AND
  auth.role() = 'authenticated' AND
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);
```

## 5. Vercel Deployment

### 5.1 Initial Setup

1. **Connect Repository**
```bash
# Login to Vercel
vercel login

# Deploy from current directory
vercel

# Follow prompts to connect GitHub repository
```

2. **Configure Project Settings**
```json
{
  "name": "3dunyam-platform",
  "framework": "nextjs",
  "buildCommand": "bun run build",
  "outputDirectory": ".next",
  "installCommand": "bun install",
  "devCommand": "bun dev"
}
```

### 5.2 Environment Variables Setup

1. **Via Vercel Dashboard**
```
Project Settings > Environment Variables

Add the following variables:
- NEXT_PUBLIC_SUPABASE_URL
- NEXT_PUBLIC_SUPABASE_ANON_KEY  
- SUPABASE_SERVICE_ROLE_KEY
- NEXT_PUBLIC_SITE_URL
```

2. **Via Vercel CLI**
```bash
# Add environment variables
vercel env add NEXT_PUBLIC_SUPABASE_URL
vercel env add SUPABASE_SERVICE_ROLE_KEY

# List environment variables
vercel env ls
```

### 5.3 Build Configuration

**vercel.json**
```json
{
  "framework": "nextjs",
  "buildCommand": "bun run build",
  "devCommand": "bun dev",
  "installCommand": "bun install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, PUT, DELETE, OPTIONS"
        },
        {
          "key": "Access-Control-Allow-Headers",
          "value": "Content-Type, Authorization"
        }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/admin/:path*",
      "destination": "/admin/:path*"
    }
  ]
}
```

**next.config.js**
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      'your-project.supabase.co',
      'images.unsplash.com'
    ],
    formats: ['image/webp', 'image/avif']
  },
  experimental: {
    serverComponentsExternalPackages: ['@supabase/supabase-js']
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  }
}

module.exports = nextConfig
```

## 6. CI/CD Pipeline

### 6.1 GitHub Actions Setup

**.github/workflows/deploy.yml**
```yaml
name: Deploy to Vercel

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
          
      - name: Install dependencies
        run: bun install
        
      - name: Run tests
        run: bun test
        
      - name: Type check
        run: bun run type-check
        
      - name: Lint
        run: bun run lint

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

### 6.2 Database Migrations in CI/CD

**.github/workflows/migrate.yml**
```yaml
name: Database Migration

on:
  push:
    branches: [main]
    paths: ['supabase/migrations/**']

jobs:
  migrate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        
      - name: Run migrations
        run: |
          supabase link --project-ref ${{ secrets.SUPABASE_PROJECT_REF }}
          supabase db push
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
```

## 7. Domain Configuration

### 7.1 Custom Domain Setup

1. **Add Domain in Vercel**
```bash
# Via CLI
vercel domains add yourdomain.com

# Via Dashboard
# Project Settings > Domains > Add Domain
```

2. **DNS Configuration**
```
# Add these DNS records at your domain provider:

Type: A
Name: @
Value: ***********

Type: CNAME  
Name: www
Value: cname.vercel-dns.com
```

### 7.2 SSL Certificate
- **Automatic**: Vercel provides automatic SSL certificates
- **Custom**: Upload custom certificates if needed
- **Renewal**: Automatic renewal every 90 days

## 8. Monitoring and Observability

### 8.1 Vercel Analytics
```typescript
// app/layout.tsx
import { Analytics } from '@vercel/analytics/react'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        {children}
        <Analytics />
      </body>
    </html>
  )
}
```

### 8.2 Error Tracking
```typescript
// lib/monitoring.ts
import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 1.0,
})

export { Sentry }
```

### 8.3 Performance Monitoring
```typescript
// lib/performance.ts
export function trackPageView(url: string) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', 'GA_MEASUREMENT_ID', {
      page_location: url,
    })
  }
}
```

## 9. Security Configuration

### 9.1 Security Headers
```javascript
// next.config.js
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
]

module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
    ]
  },
}
```

### 9.2 Environment Security
- **API Keys**: Store in Vercel environment variables
- **Database**: Use connection pooling and SSL
- **Authentication**: Implement proper JWT validation
- **CORS**: Configure appropriate CORS policies

## 10. Backup and Recovery

### 10.1 Database Backup
```bash
# Manual backup
supabase db dump --file backup.sql

# Automated backup (via Supabase)
# Supabase provides automatic daily backups
# Point-in-time recovery available
```

### 10.2 Code Backup
- **Git Repository**: Primary backup via GitHub
- **Vercel**: Automatic deployment history
- **Local**: Regular local backups

### 10.3 Recovery Procedures
1. **Database Recovery**: Use Supabase point-in-time recovery
2. **Code Recovery**: Rollback via Vercel deployments
3. **Environment Recovery**: Restore from documented configurations

## 11. Troubleshooting

### 11.1 Common Deployment Issues

**Build Failures:**
```bash
# Check build logs
vercel logs

# Local build test
bun run build

# Clear cache
vercel --force
```

**Environment Variable Issues:**
```bash
# Verify environment variables
vercel env ls

# Test locally
vercel dev
```

**Database Connection Issues:**
```bash
# Test Supabase connection
supabase status

# Check RLS policies
supabase db diff
```

### 11.2 Performance Issues
- **Bundle Analysis**: Use `@next/bundle-analyzer`
- **Image Optimization**: Verify Next.js Image component usage
- **API Performance**: Monitor API response times
- **Database Queries**: Optimize slow queries

### 11.3 Support Resources
- **Vercel Documentation**: https://vercel.com/docs
- **Supabase Documentation**: https://supabase.com/docs
- **Next.js Documentation**: https://nextjs.org/docs
- **Community Support**: GitHub Issues and Discord

---

**Document Version**: 1.0  
**Last Updated**: 2024-01-24  
**Next Review**: 2024-02-24
