# 3Dünyam E-Commerce Platform

> Modern, scalable e-commerce platform specialized for 3D printed products

[![Next.js](https://img.shields.io/badge/Next.js-14-black)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5-blue)](https://www.typescriptlang.org/)
[![Supabase](https://img.shields.io/badge/Supabase-Latest-green)](https://supabase.com/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind-3-cyan)](https://tailwindcss.com/)
[![Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black)](https://vercel.com/)

## 🚀 Overview

3Dünyam is a comprehensive e-commerce platform built specifically for 3D printed products. It features a modern, responsive design with advanced product management, inventory tracking, and order processing capabilities.

### ✨ Key Features

#### Customer Experience
- 🛍️ **Product Catalog** - Browse and search 3D printed products
- 🔍 **Advanced Filtering** - Filter by category, price, material, and more
- 🛒 **Shopping Cart** - Add, remove, and manage cart items
- 💳 **Secure Checkout** - Complete order process with address management
- 👤 **User Profiles** - Account management and order history
- 📱 **Responsive Design** - Optimized for all devices

#### Admin Management
- 📊 **Dashboard** - Comprehensive analytics and insights
- 📦 **Product Management** - Full CRUD operations for products
- 📈 **Inventory Tracking** - Real-time stock management with alerts
- 🚚 **Order Management** - Process and track customer orders
- 📥 **CSV Import** - Bulk product import from WooCommerce format
- 👥 **User Management** - Customer and admin account management

#### Technical Features
- ⚡ **High Performance** - Server-side rendering with Next.js 14
- 🔒 **Secure** - JWT authentication with Row Level Security
- 🎨 **Modern UI** - Shadcn/ui components with Tailwind CSS
- 📱 **Mobile-First** - Responsive design for all screen sizes
- 🔄 **Real-time** - Live updates for inventory and orders

## 🛠️ Technology Stack

### Frontend
- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui
- **State Management**: React Hooks + Context
- **Forms**: React Hook Form + Zod validation

### Backend
- **Database**: PostgreSQL (Supabase)
- **Authentication**: Supabase Auth
- **API**: Next.js API Routes
- **File Storage**: Supabase Storage
- **Real-time**: Supabase Realtime

### Development & Deployment
- **Package Manager**: Bun
- **Deployment**: Vercel
- **Version Control**: Git
- **CI/CD**: GitHub Actions
- **Monitoring**: Vercel Analytics

## 🚀 Quick Start

### Prerequisites
- Node.js 18.0.0 or higher
- Bun (latest version)
- Git

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/your-username/3dunyam-platform.git
cd 3dunyam-platform
```

2. **Install dependencies**
```bash
bun install
```

3. **Set up environment variables**
```bash
cp .env.example .env
```

Edit `.env` with your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

4. **Run the development server**
```bash
bun dev
```

5. **Open your browser**
Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
3dunyam-platform/
├── app/                    # Next.js 14 App Router
│   ├── (auth)/            # Authentication pages
│   ├── admin/             # Admin dashboard
│   ├── api/               # API routes
│   ├── products/          # Product pages
│   └── ...
├── components/            # Reusable components
│   ├── ui/               # Shadcn/ui components
│   ├── layout/           # Layout components
│   └── ...
├── lib/                  # Utility libraries
│   ├── auth.ts           # Authentication
│   ├── supabase.ts       # Database client
│   └── ...
├── docs/                 # Documentation
├── supabase/             # Database migrations
└── public/               # Static assets
```

## 📚 Documentation

Comprehensive documentation is available in the `/docs` directory:

- **[Product Requirements Document (PRD)](./PRD.md)** - Complete project requirements and specifications
- **[Technical Architecture](./TECHNICAL_ARCHITECTURE.md)** - System design and architecture details
- **[API Documentation](./API_DOCUMENTATION.md)** - Complete API reference
- **[Database Schema](./DATABASE_SCHEMA.md)** - Database design and relationships
- **[Development Guide](./DEVELOPMENT_GUIDE.md)** - Development workflow and coding standards
- **[Deployment Guide](./DEPLOYMENT_GUIDE.md)** - Production deployment instructions

## 🔧 Development

### Available Scripts

```bash
# Development
bun dev              # Start development server
bun build            # Build for production
bun start            # Start production server

# Code Quality
bun lint             # Run ESLint
bun type-check       # TypeScript type checking
bun format           # Format code with Prettier

# Testing
bun test             # Run tests
bun test:watch       # Run tests in watch mode
bun test:coverage    # Run tests with coverage

# Database
bun db:generate      # Generate database types
bun db:push          # Push schema changes
bun db:reset         # Reset database
```

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `NEXT_PUBLIC_SUPABASE_URL` | Supabase project URL | ✅ |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Supabase anonymous key | ✅ |
| `SUPABASE_SERVICE_ROLE_KEY` | Supabase service role key | ✅ |
| `NEXT_PUBLIC_SITE_URL` | Site URL for redirects | ✅ |

## 🚀 Deployment

### Vercel (Recommended)

1. **Connect your repository to Vercel**
2. **Set environment variables in Vercel dashboard**
3. **Deploy automatically on push to main branch**

For detailed deployment instructions, see the [Deployment Guide](./DEPLOYMENT_GUIDE.md).

### Manual Deployment

```bash
# Build the application
bun build

# Start production server
bun start
```

## 🧪 Testing

The project includes comprehensive testing:

- **Unit Tests**: Component and utility function tests
- **Integration Tests**: API endpoint tests
- **E2E Tests**: Full user journey tests

```bash
# Run all tests
bun test

# Run specific test file
bun test ProductCard.test.tsx

# Run tests with coverage
bun test --coverage
```

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. **Fork the repository**
2. **Create a feature branch** (`git checkout -b feature/amazing-feature`)
3. **Commit your changes** (`git commit -m 'Add amazing feature'`)
4. **Push to the branch** (`git push origin feature/amazing-feature`)
5. **Open a Pull Request**

### Coding Standards
- Follow TypeScript best practices
- Use conventional commit messages
- Write tests for new features
- Ensure code passes linting and type checking

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` directory
- **Issues**: [GitHub Issues](https://github.com/your-username/3dunyam-platform/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/3dunyam-platform/discussions)

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - The React framework for production
- [Supabase](https://supabase.com/) - The open source Firebase alternative
- [Tailwind CSS](https://tailwindcss.com/) - A utility-first CSS framework
- [Shadcn/ui](https://ui.shadcn.com/) - Beautifully designed components
- [Vercel](https://vercel.com/) - Platform for frontend frameworks and static sites

## 📊 Project Status

- ✅ **Core E-commerce Features** - Complete
- ✅ **Admin Dashboard** - Complete
- ✅ **User Authentication** - Complete
- ✅ **Product Management** - Complete
- ✅ **Order Processing** - Complete
- ✅ **Inventory Management** - Complete
- 🚧 **Advanced Analytics** - In Progress
- 📋 **Mobile App** - Planned

---

**Made with ❤️ for the 3D printing community**
