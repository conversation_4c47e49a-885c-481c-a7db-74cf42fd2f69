import { NextRequest, NextResponse } from 'next/server'
import { readFileSync } from 'fs'
import { join } from 'path'
import { supabase } from '@/lib/supabase'
import { importProductsFromCSV } from '@/lib/csv-import'

export async function POST(request: NextRequest) {
  try {
    // TODO: Add proper authentication check
    console.log('Direct import API called')

    console.log('Starting direct CSV import...')

    // Read the CSV file from the project root
    const csvPath = join(process.cwd(), 'products.csv')
    
    let csvContent: string
    try {
      csvContent = readFileSync(csvPath, 'utf-8')
    } catch (error) {
      console.error('Error reading CSV file:', error)
      return NextResponse.json(
        { error: 'CSV dosyası bulunamadı veya okunamadı' },
        { status: 400 }
      )
    }

    if (!csvContent.trim()) {
      return NextResponse.json(
        { error: 'CSV dosyası boş' },
        { status: 400 }
      )
    }

    console.log('CSV file loaded, processing...')

    // Process the CSV import
    const result = await importProductsFromCSV(csvContent)

    console.log('Import completed:', result)

    return NextResponse.json(result)
  } catch (error) {
    console.error('Direct import API error:', error)
    return NextResponse.json(
      { 
        error: 'Sunucu hatası oluştu',
        details: error instanceof Error ? error.message : 'Bilinmeyen hata'
      },
      { status: 500 }
    )
  }
}
