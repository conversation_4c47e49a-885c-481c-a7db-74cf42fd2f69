-- Add missing columns to orders table

-- Add payment_method column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'orders' AND column_name = 'payment_method') THEN
        ALTER TABLE orders ADD COLUMN payment_method text DEFAULT 'cash-on-delivery';
    END IF;
END $$;

-- Add shipping_cost column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'orders' AND column_name = 'shipping_cost') THEN
        ALTER TABLE orders ADD COLUMN shipping_cost decimal(10,2) DEFAULT 0;
    END IF;
END $$;

-- Add total column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'orders' AND column_name = 'total') THEN
        ALTER TABLE orders ADD COLUMN total decimal(10,2);
    END IF;
END $$;

-- Update existing records to populate new columns
UPDATE orders 
SET 
  shipping_cost = COALESCE(shipping_amount, 0),
  total = COALESCE(total_amount, subtotal + COALESCE(tax_amount, 0) + COALESCE(shipping_amount, 0))
WHERE shipping_cost IS NULL OR total IS NULL;

-- Add index for payment method if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_orders_payment_method') THEN
        CREATE INDEX idx_orders_payment_method ON orders(payment_method);
    END IF;
END $$;
