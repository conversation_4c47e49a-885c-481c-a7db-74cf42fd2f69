import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseAdmin } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const supabaseAdmin = createSupabaseAdmin()
    
    // Try to get an existing user first
    const { data: existingProfiles } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .limit(1)

    let testUserId = 'test-user-123'

    if (existingProfiles && existingProfiles.length > 0) {
      testUserId = existingProfiles[0].id
    } else {
      // Create a test user if no users exist
      const { data: newProfile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .insert({
          id: testUserId,
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User',
          phone: '+90 ************'
        })
        .select()
        .single()

      if (profileError && profileError.code !== '23505') { // Ignore duplicate key error
        console.error('Profile creation error:', profileError)
        // If profile creation fails, try to use a different ID
        testUserId = 'f3821fe6-68e1-43f8-9c7d-3e4162301ffb' // fallback user ID
      }
    }

    // Create test order
    const orderNumber = `ORD-${Date.now().toString().slice(-8)}`
    
    const { data: order, error: orderError } = await supabaseAdmin
      .from('orders')
      .insert({
        user_id: testUserId,
        order_number: orderNumber,
        status: 'pending',
        subtotal: 100.00,
        total_amount: 129.90,
        shipping_amount: 29.90,
        tax_amount: 0,
        currency: 'TRY',
        shipping_address: {
          name: 'Test User',
          address: 'Test Mahallesi Test Sokak No:1',
          city: 'İstanbul',
          postal_code: '34000',
          country: 'TR',
          phone: '+90 ************'
        },
        billing_address: {
          name: 'Test User',
          address: 'Test Mahallesi Test Sokak No:1',
          city: 'İstanbul',
          postal_code: '34000',
          country: 'TR',
          phone: '+90 ************'
        },
        notes: 'Test siparişi'
      })
      .select()
      .single()

    if (orderError) {
      throw orderError
    }

    // Create test order items
    const { data: orderItems, error: itemsError } = await supabaseAdmin
      .from('order_items')
      .insert([
        {
          order_id: order.id,
          product_id: 'test-product-1',
          quantity: 2,
          price: 50.00,
          total: 100.00
        }
      ])
      .select()

    if (itemsError) {
      console.error('Order items error:', itemsError)
    }

    return NextResponse.json({
      success: true,
      message: 'Test siparişi oluşturuldu',
      data: {
        order,
        orderItems
      }
    })
  } catch (error) {
    console.error('Test order creation error:', error)
    return NextResponse.json(
      { error: 'Test siparişi oluşturulamadı', details: error },
      { status: 500 }
    )
  }
}
