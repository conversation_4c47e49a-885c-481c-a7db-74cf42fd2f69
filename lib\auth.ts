import { supabase } from './supabase'
import { Database } from './database.types'

export type User = Database['public']['Tables']['profiles']['Row']

export const signUp = async (email: string, password: string, userData: {
  first_name: string
  last_name: string
  phone?: string
}) => {
  // Sign up the user
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        first_name: userData.first_name,
        last_name: userData.last_name,
        phone: userData.phone || '',
      }
    }
  })

  if (error) throw error

  // If user is created, manually create profile
  if (data.user) {
    console.log('User created:', data.user.id)

    try {
      // Create profile manually and wait for completion
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: data.user.id,
          email: data.user.email || email,
          first_name: userData.first_name,
          last_name: userData.last_name,
          phone: userData.phone || '',
          role: 'customer'
        })
        .select()
        .single()

      if (profileError) {
        console.error('Profile creation error:', profileError)
        // Don't throw error, user is already created
      } else {
        console.log('Profile created successfully:', profileData)
      }
    } catch (profileError) {
      console.error('Failed to create profile:', profileError)
    }
  }

  return data
}

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })

  if (error) throw error
  return data
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}

export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) return null

  const { data: profile, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  // If profile doesn't exist, create it
  if (error && error.code === 'PGRST116') {
    console.log('Profile not found, creating one for user:', user.id)

    const { data: newProfile, error: createError } = await supabase
      .from('profiles')
      .insert({
        id: user.id,
        email: user.email || '',
        first_name: user.user_metadata?.first_name || '',
        last_name: user.user_metadata?.last_name || '',
        phone: user.user_metadata?.phone || '',
        role: 'customer'
      })
      .select()
      .single()

    if (createError) {
      console.error('Failed to create profile:', createError)
      return null
    }

    console.log('Profile created in getCurrentUser:', newProfile)
    return newProfile
  }

  if (error) {
    console.error('Error fetching profile:', error)
    return null
  }

  return profile
}

export const updateProfile = async (userId: string, updates: Partial<User>) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()

  if (error) throw error
  return data
}

// Server-side authentication for API routes
export const getCurrentUserFromRequest = async (request: Request) => {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null
    }

    const token = authHeader.substring(7)

    // Verify JWT token with Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token)

    if (error || !user) {
      return null
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError) {
      return null
    }

    return profile
  } catch (error) {
    console.error('Error getting user from request:', error)
    return null
  }
}