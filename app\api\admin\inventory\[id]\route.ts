import { NextRequest, NextResponse } from 'next/server'
import { updateInventory } from '@/lib/inventory'

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const productId = id
    const inventoryData = await request.json()
    
    await updateInventory(productId, {
      quantity: inventoryData.quantity,
      reserved_quantity: inventoryData.reserved_quantity,
      reorder_level: inventoryData.reorder_level
    })
    
    return NextResponse.json({ 
      success: true, 
      message: 'Stok bilgileri güncellendi' 
    })
  } catch (error) {
    console.error('Inventory update API error:', error)
    return NextResponse.json(
      { error: 'Stok güncellenemedi' },
      { status: 500 }
    )
  }
}
