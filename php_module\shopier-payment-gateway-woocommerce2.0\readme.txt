=== WooCommerce Shopier Module ===
Contributors: Shopier
Requires at least Wordpress: 3.0.0
Tested up to WooCommerce: 2.6.4

Allows you to use Pay Credit Card with the WooCommerce plugin.

== Description ==

Shopier Module for WooCommerce

Visit [https://www.shopier.com]
== Installation ==
1. You should have latest version of WooCommerce plugin installed
2. Unzip and upload plugin folder to your /wp-content/plugins/ directory  OR Go to wp-admin > plugins > Add new Plugin & Upload plugin zip.
3. Go to wp-admin > Plugins(left menu) > Activate the plugin

== Screenshots ==
1. WooCommerce payment gateway setting page
2. Pay with Credit Card setting page
3. Checkout Page Payment gateway listing


== Configuration ==

1. Visit the WooCommerce settings page, and click on the Payment Gateways/Checkout tab.
2. Enable the Payment Method, name it Shopier Payment Gateway (this will show up on the payment page your customer sees), add in your api key and secret and set shopier api end points. Click Save.