'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { toast } from 'sonner'
import { Settings, Truck, Globe, Mail, Phone, Save } from 'lucide-react'

interface ShippingSettings {
  shipping_cost: {
    amount: number
    currency: string
  }
  free_shipping_threshold: {
    amount: number
    currency: string
  }
  shipping_enabled: boolean
}

interface GeneralSettings {
  site_name: string
  site_description: string
  contact_email: string
  contact_phone: string
}

export default function AdminSettingsPage() {
  const [shippingSettings, setShippingSettings] = useState<ShippingSettings>({
    shipping_cost: { amount: 29.90, currency: 'TRY' },
    free_shipping_threshold: { amount: 500, currency: 'TRY' },
    shipping_enabled: true
  })

  const [generalSettings, setGeneralSettings] = useState<GeneralSettings>({
    site_name: '3Dünyam',
    site_description: '3D Baskı Ürünleri E-Ticaret Platformu',
    contact_email: '<EMAIL>',
    contact_phone: '+90 ************'
  })

  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      // Load shipping settings
      const shippingResponse = await fetch('/api/admin/settings/shipping')
      if (shippingResponse.ok) {
        const shippingData = await shippingResponse.json()
        setShippingSettings(shippingData.data)
      }

      // Load general settings
      const generalResponse = await fetch('/api/admin/settings/general')
      if (generalResponse.ok) {
        const generalData = await generalResponse.json()
        setGeneralSettings(generalData.data)
      }
    } catch (error) {
      console.error('Error loading settings:', error)
      toast.error('Ayarlar yüklenemedi')
    } finally {
      setIsLoading(false)
    }
  }

  const saveShippingSettings = async () => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/admin/settings/shipping', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(shippingSettings),
      })

      if (response.ok) {
        toast.success('Kargo ayarları başarıyla güncellendi')
      } else {
        throw new Error('Failed to save shipping settings')
      }
    } catch (error) {
      console.error('Error saving shipping settings:', error)
      toast.error('Kargo ayarları güncellenemedi')
    } finally {
      setIsSaving(false)
    }
  }

  const saveGeneralSettings = async () => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/admin/settings/general', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(generalSettings),
      })

      if (response.ok) {
        toast.success('Genel ayarlar başarıyla güncellendi')
      } else {
        throw new Error('Failed to save general settings')
      }
    } catch (error) {
      console.error('Error saving general settings:', error)
      toast.error('Genel ayarlar güncellenemedi')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Site Ayarları</h1>
          <p className="text-gray-600 mt-2">
            Site genelinde kullanılan ayarları yönetin
          </p>
        </div>
        <Settings className="h-8 w-8 text-gray-400" />
      </div>

      <Tabs defaultValue="shipping" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="shipping" className="flex items-center gap-2">
            <Truck className="h-4 w-4" />
            Kargo Ayarları
          </TabsTrigger>
          <TabsTrigger value="general" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Genel Ayarlar
          </TabsTrigger>
        </TabsList>

        {/* Shipping Settings */}
        <TabsContent value="shipping">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Truck className="h-5 w-5" />
                Kargo Ayarları
              </CardTitle>
              <CardDescription>
                Kargo ücretleri ve ücretsiz kargo limitlerini ayarlayın
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">Kargo Hizmeti</Label>
                  <div className="text-sm text-gray-500">
                    Kargo hizmetini aktif/pasif yapın
                  </div>
                </div>
                <Switch
                  checked={shippingSettings.shipping_enabled}
                  onCheckedChange={(checked) =>
                    setShippingSettings(prev => ({
                      ...prev,
                      shipping_enabled: checked
                    }))
                  }
                />
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="shipping-cost">Kargo Ücreti (₺)</Label>
                  <Input
                    id="shipping-cost"
                    type="number"
                    step="0.01"
                    min="0"
                    value={shippingSettings.shipping_cost.amount}
                    onChange={(e) =>
                      setShippingSettings(prev => ({
                        ...prev,
                        shipping_cost: {
                          ...prev.shipping_cost,
                          amount: parseFloat(e.target.value) || 0
                        }
                      }))
                    }
                    disabled={!shippingSettings.shipping_enabled}
                  />
                  <p className="text-sm text-gray-500">
                    Standart kargo ücreti
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="free-shipping-threshold">Ücretsiz Kargo Limiti (₺)</Label>
                  <Input
                    id="free-shipping-threshold"
                    type="number"
                    step="0.01"
                    min="0"
                    value={shippingSettings.free_shipping_threshold.amount}
                    onChange={(e) =>
                      setShippingSettings(prev => ({
                        ...prev,
                        free_shipping_threshold: {
                          ...prev.free_shipping_threshold,
                          amount: parseFloat(e.target.value) || 0
                        }
                      }))
                    }
                    disabled={!shippingSettings.shipping_enabled}
                  />
                  <p className="text-sm text-gray-500">
                    Bu tutarın üzerindeki siparişlerde kargo ücretsiz
                  </p>
                </div>
              </div>

              <div className="flex justify-end">
                <Button 
                  onClick={saveShippingSettings}
                  disabled={isSaving}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  {isSaving ? 'Kaydediliyor...' : 'Kaydet'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* General Settings */}
        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Genel Ayarlar
              </CardTitle>
              <CardDescription>
                Site bilgileri ve iletişim ayarları
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="site-name">Site Adı</Label>
                  <Input
                    id="site-name"
                    value={generalSettings.site_name}
                    onChange={(e) =>
                      setGeneralSettings(prev => ({
                        ...prev,
                        site_name: e.target.value
                      }))
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="site-description">Site Açıklaması</Label>
                  <Input
                    id="site-description"
                    value={generalSettings.site_description}
                    onChange={(e) =>
                      setGeneralSettings(prev => ({
                        ...prev,
                        site_description: e.target.value
                      }))
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contact-email" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    İletişim E-postası
                  </Label>
                  <Input
                    id="contact-email"
                    type="email"
                    value={generalSettings.contact_email}
                    onChange={(e) =>
                      setGeneralSettings(prev => ({
                        ...prev,
                        contact_email: e.target.value
                      }))
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contact-phone" className="flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    İletişim Telefonu
                  </Label>
                  <Input
                    id="contact-phone"
                    value={generalSettings.contact_phone}
                    onChange={(e) =>
                      setGeneralSettings(prev => ({
                        ...prev,
                        contact_phone: e.target.value
                      }))
                    }
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button 
                  onClick={saveGeneralSettings}
                  disabled={isSaving}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  {isSaving ? 'Kaydediliyor...' : 'Kaydet'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
