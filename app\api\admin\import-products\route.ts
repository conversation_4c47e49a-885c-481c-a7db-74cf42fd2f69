import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { importProductsFromCSV } from '@/lib/csv-import'

export async function POST(request: NextRequest) {
  try {
    // TODO: Add proper authentication check
    console.log('CSV import API called')

    // Get the uploaded file
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { error: 'Dosya bulunamadı' },
        { status: 400 }
      )
    }

    // Validate file type
    if (!file.name.endsWith('.csv') && file.type !== 'text/csv') {
      return NextResponse.json(
        { error: 'Geçersiz dosya türü. Lütfen CSV dosyası yükleyin.' },
        { status: 400 }
      )
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'Dosya boyutu çok büyük. Maksimum 10MB olmalıdır.' },
        { status: 400 }
      )
    }

    // Read file content
    const csvContent = await file.text()

    if (!csvContent.trim()) {
      return NextResponse.json(
        { error: 'Dosya boş' },
        { status: 400 }
      )
    }

    // Process the CSV import
    const result = await importProductsFromCSV(csvContent)

    return NextResponse.json(result)
  } catch (error) {
    console.error('Import API error:', error)
    return NextResponse.json(
      { 
        error: 'Sunucu hatası oluştu',
        details: error instanceof Error ? error.message : 'Bilinmeyen hata'
      },
      { status: 500 }
    )
  }
}
