import { supabase, createSupabaseAdmin } from './supabase'
import { Database } from './database.types'

export type Product = Database['public']['Tables']['products']['Row'] & {
  category?: Database['public']['Tables']['categories']['Row']
  images?: Database['public']['Tables']['product_images']['Row'][]
  inventory?: Database['public']['Tables']['inventory']['Row']
}

export type Category = Database['public']['Tables']['categories']['Row']

export const getProducts = async (filters?: {
  category?: string
  featured?: boolean
  search?: string
  limit?: number
  offset?: number
  includeInactive?: boolean
}) => {
  let query = supabase
    .from('products')
    .select(`
      *,
      category:categories(*),
      images:product_images(*),
      inventory(*)
    `)
    .order('created_at', { ascending: false })

  // Only filter by is_active if includeInactive is not true
  if (!filters?.includeInactive) {
    query = query.eq('is_active', true)
  }

  if (filters?.category) {
    query = query.eq('category_id', filters.category)
  }

  if (filters?.featured) {
    query = query.eq('is_featured', true)
  }

  if (filters?.search) {
    query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
  }

  if (filters?.limit) {
    query = query.limit(filters.limit)
  }

  if (filters?.offset) {
    query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
  }

  const { data, error } = await query

  if (error) throw error
  return data as Product[]
}

export const getProduct = async (slug: string) => {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      category:categories(*),
      images:product_images(*),
      inventory(*)
    `)
    .eq('slug', slug)
    .eq('is_active', true)
    .single()

  if (error) throw error
  return data
}

export const getProductById = async (id: string) => {
  const supabaseAdmin = createSupabaseAdmin()
  const { data, error } = await supabaseAdmin
    .from('products')
    .select(`
      *,
      category:categories(*),
      images:product_images(*),
      inventory(*)
    `)
    .eq('id', id)
    .single()

  if (error) throw error
  return data as Product
}

export const getFeaturedProducts = async (limit = 8) => {
  return getProducts({ featured: true, limit })
}

export const getCategories = async () => {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .eq('is_active', true)
    .order('sort_order')

  if (error) throw error
  return data as Category[]
}

export const searchProducts = async (query: string, limit = 20) => {
  return getProducts({ search: query, limit })
}

export const getProductsByCategory = async (categorySlug: string, limit = 20) => {
  const { data: category } = await supabase
    .from('categories')
    .select('id')
    .eq('slug', categorySlug)
    .single()

  if (!category) return []

  return getProducts({ category: category.id, limit })
}

// Admin functions for product management
export const createProduct = async (productData: Database['public']['Tables']['products']['Insert']) => {
  const supabaseAdmin = createSupabaseAdmin()
  const { data, error } = await supabaseAdmin
    .from('products')
    .insert(productData)
    .select()
    .single()

  if (error) throw error
  return data
}

export const updateProduct = async (id: string, productData: Database['public']['Tables']['products']['Update']) => {
  const supabaseAdmin = createSupabaseAdmin()
  const { data, error } = await supabaseAdmin
    .from('products')
    .update(productData)
    .eq('id', id)
    .select()
    .single()

  if (error) throw error
  return data
}

export const deleteProduct = async (id: string) => {
  const supabaseAdmin = createSupabaseAdmin()

  // Check if product is referenced in orders
  const { data: orderItems, error: checkError } = await supabaseAdmin
    .from('order_items')
    .select('id')
    .eq('product_id', id)
    .limit(1)

  if (checkError) throw checkError

  if (orderItems && orderItems.length > 0) {
    // Product is referenced in orders, use soft delete
    const { error } = await supabaseAdmin
      .from('products')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)

    if (error) throw error
    return { type: 'soft_delete', message: 'Ürün siparişlerde kullanıldığı için deaktif edildi' }
  } else {
    // Product is not referenced, safe to hard delete
    const { error } = await supabaseAdmin
      .from('products')
      .delete()
      .eq('id', id)

    if (error) throw error
    return { type: 'hard_delete', message: 'Ürün başarıyla silindi' }
  }
}

export const createCategory = async (categoryData: Database['public']['Tables']['categories']['Insert']) => {
  const supabaseAdmin = createSupabaseAdmin()
  const { data, error } = await supabaseAdmin
    .from('categories')
    .insert(categoryData)
    .select()
    .single()

  if (error) throw error
  return data
}

export const createProductImage = async (imageData: Database['public']['Tables']['product_images']['Insert']) => {
  const supabaseAdmin = createSupabaseAdmin()
  const { data, error } = await supabaseAdmin
    .from('product_images')
    .insert(imageData)
    .select()
    .single()

  if (error) throw error
  return data
}

export const createInventory = async (inventoryData: Database['public']['Tables']['inventory']['Insert']) => {
  const supabaseAdmin = createSupabaseAdmin()
  const { data, error } = await supabaseAdmin
    .from('inventory')
    .insert(inventoryData)
    .select()
    .single()

  if (error) throw error
  return data
}

export const updateProductImages = async (productId: string, images: { image_url: string; alt_text: string; sort_order: number; is_primary: boolean }[]) => {
  const supabaseAdmin = createSupabaseAdmin()

  // First, delete existing images
  await supabaseAdmin
    .from('product_images')
    .delete()
    .eq('product_id', productId)

  // Then, insert new images
  if (images.length > 0) {
    const imageData = images.map(img => ({
      product_id: productId,
      image_url: img.image_url,
      alt_text: img.alt_text,
      sort_order: img.sort_order,
      is_primary: img.is_primary
    }))

    const { data, error } = await supabaseAdmin
      .from('product_images')
      .insert(imageData)
      .select()

    if (error) throw error
    return data
  }

  return []
}

export const updateProductInventory = async (productId: string, inventoryData: { quantity: number; reserved_quantity: number; reorder_level: number }) => {
  const supabaseAdmin = createSupabaseAdmin()

  // Check if inventory exists
  const { data: existingInventory } = await supabaseAdmin
    .from('inventory')
    .select('id')
    .eq('product_id', productId)
    .single()

  if (existingInventory) {
    // Update existing inventory
    const { data, error } = await supabaseAdmin
      .from('inventory')
      .update({
        quantity: inventoryData.quantity,
        reserved_quantity: inventoryData.reserved_quantity,
        reorder_level: inventoryData.reorder_level,
        updated_at: new Date().toISOString()
      })
      .eq('product_id', productId)
      .select()
      .single()

    if (error) throw error
    return data
  } else {
    // Create new inventory
    const { data, error } = await supabaseAdmin
      .from('inventory')
      .insert({
        product_id: productId,
        quantity: inventoryData.quantity,
        reserved_quantity: inventoryData.reserved_quantity,
        reorder_level: inventoryData.reorder_level
      })
      .select()
      .single()

    if (error) throw error
    return data
  }
}