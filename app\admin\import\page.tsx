'use client'

import { useState, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Upload, FileText, CheckCircle, XCircle, AlertTriangle, Download } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface ImportResult {
  success: boolean
  message: string
  details?: {
    totalRows: number
    successfulImports: number
    failedImports: number
    errors: string[]
  }
}

export default function ImportPage() {
  const [file, setFile] = useState<File | null>(null)
  const [isImporting, setIsImporting] = useState(false)
  const [progress, setProgress] = useState(0)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
        toast.error('Lütfen geçerli bir CSV dosyası seçin')
        return
      }
      setFile(selectedFile)
      setImportResult(null)
    }
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile && (droppedFile.type === 'text/csv' || droppedFile.name.endsWith('.csv'))) {
      setFile(droppedFile)
      setImportResult(null)
    } else {
      toast.error('Lütfen geçerli bir CSV dosyası sürükleyin')
    }
  }

  const handleImport = async () => {
    if (!file) {
      toast.error('Lütfen önce bir dosya seçin')
      return
    }

    setIsImporting(true)
    setProgress(0)
    setImportResult(null)

    try {
      const formData = new FormData()
      formData.append('file', file)

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90))
      }, 500)

      const response = await fetch('/api/admin/import-products', {
        method: 'POST',
        body: formData,
      })

      clearInterval(progressInterval)
      setProgress(100)

      const result = await response.json()
      
      if (response.ok) {
        setImportResult(result)
        toast.success('İçe aktarma tamamlandı!')
      } else {
        setImportResult({
          success: false,
          message: result.error || 'İçe aktarma sırasında hata oluştu'
        })
        toast.error('İçe aktarma başarısız')
      }
    } catch (error) {
      console.error('Import error:', error)
      setImportResult({
        success: false,
        message: 'Sunucu hatası oluştu'
      })
      toast.error('İçe aktarma sırasında hata oluştu')
    } finally {
      setIsImporting(false)
      setProgress(0)
    }
  }

  const downloadSampleCSV = () => {
    const sampleData = `Kimlik,Tür,Stok kodu (SKU),İsim,Yayımlanmış,Öne çıkan?,Kısa açıklama,Açıklama,Normal fiyat,Kategoriler,Görseller
1,simple,SAMPLE-001,Örnek Ürün,1,0,Kısa açıklama,Detaylı açıklama,100,Kategori 1,https://example.com/image.jpg
2,simple,SAMPLE-002,Başka Ürün,1,1,Başka kısa açıklama,Başka detaylı açıklama,200,Kategori 2,https://example.com/image2.jpg`
    
    const blob = new Blob([sampleData], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'ornek-urunler.csv'
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">CSV İçe Aktarma</h1>
        <p className="text-gray-600 mt-2">
          WooCommerce formatındaki CSV dosyalarından ürünleri sisteme aktarın
        </p>
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            Kullanım Talimatları
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Desteklenen Formatlar:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• WooCommerce CSV Export formatı</li>
                <li>• UTF-8 kodlaması</li>
                <li>• Maksimum dosya boyutu: 10MB</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Gerekli Alanlar:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• İsim (Name)</li>
                <li>• SKU (Stok Kodu)</li>
                <li>• Fiyat (Price)</li>
                <li>• Açıklama (Description)</li>
              </ul>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={downloadSampleCSV}>
              <Download className="mr-2 h-4 w-4" />
              Örnek CSV İndir
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* File Upload */}
      <Card>
        <CardHeader>
          <CardTitle>Dosya Yükleme</CardTitle>
          <CardDescription>
            CSV dosyanızı seçin veya sürükleyip bırakın
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            {file ? (
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-900">{file.name}</p>
                <p className="text-xs text-gray-500">
                  {(file.size / 1024 / 1024).toFixed(2)} MB
                </p>
                <Badge variant="outline" className="text-green-600 border-green-600">
                  Dosya Seçildi
                </Badge>
              </div>
            ) : (
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  CSV dosyanızı buraya sürükleyin veya
                </p>
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Dosya Seç
                </Button>
              </div>
            )}
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>

          {file && (
            <div className="mt-6 flex gap-4">
              <Button
                onClick={handleImport}
                disabled={isImporting}
                className="flex-1"
              >
                {isImporting ? 'İçe Aktarılıyor...' : 'İçe Aktarmaya Başla'}
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setFile(null)
                  setImportResult(null)
                  if (fileInputRef.current) {
                    fileInputRef.current.value = ''
                  }
                }}
                disabled={isImporting}
              >
                Temizle
              </Button>
            </div>
          )}

          {isImporting && (
            <div className="mt-6 space-y-2">
              <div className="flex justify-between text-sm">
                <span>İlerleme</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Import Results */}
      {importResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              {importResult.success ? (
                <CheckCircle className="mr-2 h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="mr-2 h-5 w-5 text-red-600" />
              )}
              İçe Aktarma Sonucu
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert className={importResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              <AlertDescription>
                {importResult.message}
              </AlertDescription>
            </Alert>

            {importResult.details && (
              <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {importResult.details.totalRows}
                  </div>
                  <div className="text-sm text-gray-600">Toplam Satır</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {importResult.details.successfulImports}
                  </div>
                  <div className="text-sm text-gray-600">Başarılı</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {importResult.details.failedImports}
                  </div>
                  <div className="text-sm text-gray-600">Başarısız</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {Math.round((importResult.details.successfulImports / importResult.details.totalRows) * 100)}%
                  </div>
                  <div className="text-sm text-gray-600">Başarı Oranı</div>
                </div>
              </div>
            )}

            {importResult.details?.errors && importResult.details.errors.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium text-red-800 mb-2 flex items-center">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Hatalar:
                </h4>
                <div className="bg-red-50 border border-red-200 rounded p-3 max-h-40 overflow-y-auto">
                  {importResult.details.errors.map((error, index) => (
                    <div key={index} className="text-sm text-red-700 mb-1">
                      {error}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
