'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { getProducts, getCategories } from '@/lib/products'
import { Package, Upload, ShoppingCart, TrendingUp, AlertTriangle, Plus } from 'lucide-react'
import { toast } from 'react-hot-toast'

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalCategories: 0,
    totalOrders: 0,
    totalUsers: 0,
    lowStockProducts: 0,
    totalRevenue: 0,
    pendingOrders: 0,
    outOfStockProducts: 0,
    recentActivity: []
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      // Load basic data that doesn't require admin privileges
      const [products, categories] = await Promise.all([
        getProducts({ limit: 1000 }),
        getCategories()
      ])

      setStats({
        totalProducts: products.length,
        totalCategories: categories.length,
        totalOrders: 0, // Will be loaded via API
        totalUsers: 0,
        lowStockProducts: products.filter(p => p.inventory && p.inventory.quantity < 10).length,
        totalRevenue: 0, // Will be loaded via API
        pendingOrders: 0, // Will be loaded via API
        outOfStockProducts: products.filter(p => !p.inventory || p.inventory.quantity === 0).length,
        recentActivity: []
      })

      // Load admin stats via API
      loadAdminStats()
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadAdminStats = async () => {
    try {
      const response = await fetch('/api/admin/stats')
      if (response.ok) {
        const adminStats = await response.json()
        console.log('Admin stats loaded:', adminStats)
        setStats(prev => ({
          ...prev,
          totalOrders: adminStats.total_orders || 0,
          totalRevenue: adminStats.total_revenue || 0,
          pendingOrders: adminStats.pending_orders || 0
        }))
      }
    } catch (error) {
      console.error('Error loading admin stats:', error)
    }
  }

  const createTestOrder = async () => {
    try {
      const response = await fetch('/api/test/create-order', { method: 'POST' })
      const result = await response.json()
      if (result.success) {
        toast.success('Test siparişi oluşturuldu')
        loadAdminStats() // Reload stats
      } else {
        toast.error('Test siparişi oluşturulamadı')
      }
    } catch (error) {
      toast.error('Hata oluştu')
    }
  }

  const statCards = [
    {
      title: 'Toplam Ürün',
      value: stats.totalProducts,
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Toplam Sipariş',
      value: stats.totalOrders,
      icon: ShoppingCart,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Bekleyen Sipariş',
      value: stats.pendingOrders,
      icon: AlertTriangle,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    },
    {
      title: 'Toplam Gelir',
      value: `${stats.totalRevenue.toLocaleString('tr-TR')} ₺`,
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    }
  ]

  if (isLoading) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={createTestOrder}>
            <Plus className="mr-2 h-4 w-4" />
            Test Siparişi Oluştur
          </Button>
          <Badge variant="outline" className="text-green-600 border-green-600">
            Sistem Aktif
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Alerts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {stats.lowStockProducts > 0 && (
          <Card className="border-orange-200 bg-orange-50">
            <CardHeader>
              <CardTitle className="flex items-center text-orange-800">
                <AlertTriangle className="mr-2 h-5 w-5" />
                Düşük Stok Uyarısı
              </CardTitle>
              <CardDescription className="text-orange-700">
                {stats.lowStockProducts} ürünün stoğu düşük seviyede
              </CardDescription>
            </CardHeader>
          </Card>
        )}

        {stats.outOfStockProducts > 0 && (
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="flex items-center text-red-800">
                <AlertTriangle className="mr-2 h-5 w-5" />
                Stokta Yok Uyarısı
              </CardTitle>
              <CardDescription className="text-red-700">
                {stats.outOfStockProducts} ürün stokta yok
              </CardDescription>
            </CardHeader>
          </Card>
        )}

        {stats.pendingOrders > 0 && (
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="flex items-center text-blue-800">
                <ShoppingCart className="mr-2 h-5 w-5" />
                Bekleyen Siparişler
              </CardTitle>
              <CardDescription className="text-blue-700">
                {stats.pendingOrders} sipariş onay bekliyor
              </CardDescription>
            </CardHeader>
          </Card>
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Hızlı İşlemler</CardTitle>
            <CardDescription>
              Sık kullanılan admin işlemleri
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <a
                href="/admin/products"
                className="flex items-center p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
              >
                <Package className="mr-3 h-5 w-5 text-blue-600" />
                <span className="text-sm font-medium">Ürün Yönetimi</span>
              </a>
              <a
                href="/admin/orders"
                className="flex items-center p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
              >
                <ShoppingCart className="mr-3 h-5 w-5 text-purple-600" />
                <span className="text-sm font-medium">Sipariş Yönetimi</span>
              </a>
              <a
                href="/admin/inventory"
                className="flex items-center p-3 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors"
              >
                <AlertTriangle className="mr-3 h-5 w-5 text-yellow-600" />
                <span className="text-sm font-medium">Stok Yönetimi</span>
              </a>
              <a
                href="/admin/import"
                className="flex items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
              >
                <Upload className="mr-3 h-5 w-5 text-green-600" />
                <span className="text-sm font-medium">CSV İçe Aktar</span>
              </a>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Son Aktiviteler</CardTitle>
            <CardDescription>
              Sistem üzerindeki son işlemler
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-gray-500">
              Henüz aktivite bulunmuyor
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
