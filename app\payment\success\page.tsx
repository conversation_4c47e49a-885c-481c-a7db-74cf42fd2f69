'use client'

import { useEffect, useState, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { CheckCircle, Package, Home, ArrowRight, CreditCard } from 'lucide-react'
import { getShopierText } from '@/lib/shopier'

function PaymentSuccessContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [orderDetails, setOrderDetails] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  const orderId = searchParams.get('order_id')
  const paymentId = searchParams.get('payment_id')
  const installment = searchParams.get('installment')

  useEffect(() => {
    if (!orderId) {
      router.push('/')
      return
    }

    // Load order details
    loadOrderDetails()
  }, [orderId, router])

  const loadOrderDetails = async () => {
    try {
      // You can fetch order details here if needed
      // For now, we'll use mock data
      setOrderDetails({
        id: orderId,
        order_number: `ORD-${Date.now().toString().slice(-6)}`,
        total: 0, // Will be loaded from API
        status: 'confirmed'
      })
    } catch (error) {
      console.error('Error loading order details:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <div className="animate-pulse space-y-4">
            <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto"></div>
            <div className="h-8 bg-gray-200 rounded w-3/4 mx-auto"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-2xl mx-auto text-center">
        {/* Success Icon */}
        <div className="mb-8">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="h-12 w-12 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {getShopierText('Shopier payment successful', 'tr')}
          </h1>
          <p className="text-gray-600 text-lg">
            {getShopierText('Thank you for shopping with us. Your account has been charged and your transaction is successful.', 'tr')}
          </p>
        </div>

        {/* Payment Details */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center justify-center space-x-2">
              <CreditCard className="h-5 w-5" />
              <span>Ödeme Detayları</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              {orderId && (
                <div>
                  <span className="text-gray-600">Sipariş ID:</span>
                  <p className="font-medium">{orderId}</p>
                </div>
              )}
              
              {paymentId && (
                <div>
                  <span className="text-gray-600">Ödeme ID:</span>
                  <p className="font-medium">{paymentId}</p>
                </div>
              )}
              
              <div>
                <span className="text-gray-600">Ödeme Tarihi:</span>
                <p className="font-medium">{new Date().toLocaleDateString('tr-TR')}</p>
              </div>
              
              {installment && installment !== '1' && (
                <div>
                  <span className="text-gray-600">Taksit:</span>
                  <p className="font-medium">{installment} Taksit</p>
                </div>
              )}
              
              <div>
                <span className="text-gray-600">Ödeme Yöntemi:</span>
                <p className="font-medium">Kredi Kartı (Shopier)</p>
              </div>
              
              <div>
                <span className="text-gray-600">Durum:</span>
                <p className="font-medium text-green-600">Onaylandı</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Sonraki Adımlar</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-blue-600 font-semibold text-sm">1</span>
                </div>
                <div className="text-left">
                  <h3 className="font-medium">Sipariş Onayı</h3>
                  <p className="text-sm text-gray-600">
                    E-posta adresinize sipariş onay maili gönderildi.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Package className="h-4 w-4 text-yellow-600" />
                </div>
                <div className="text-left">
                  <h3 className="font-medium">Hazırlık Süreci</h3>
                  <p className="text-sm text-gray-600">
                    Siparişiniz 1-2 iş günü içinde hazırlanacak.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Package className="h-4 w-4 text-green-600" />
                </div>
                <div className="text-left">
                  <h3 className="font-medium">Kargo & Teslimat</h3>
                  <p className="text-sm text-gray-600">
                    Kargo takip numarası e-posta ile gönderilecek.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/products">
                Alışverişe Devam Et
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            
            <Button asChild variant="outline" size="lg">
              <Link href="/profile">
                <Package className="mr-2 h-5 w-5" />
                Siparişlerim
              </Link>
            </Button>
            
            <Button asChild variant="outline" size="lg">
              <Link href="/">
                <Home className="mr-2 h-5 w-5" />
                Ana Sayfa
              </Link>
            </Button>
          </div>
        </div>

        {/* Security Info */}
        <div className="mt-12 p-6 bg-green-50 rounded-lg">
          <h3 className="font-semibold text-green-900 mb-2">
            Güvenli Ödeme Tamamlandı
          </h3>
          <div className="text-sm text-green-800 space-y-1">
            <p>• Ödemeniz Shopier güvencesi ile alınmıştır</p>
            <p>• 256-bit SSL şifreleme ile korunmuştur</p>
            <p>• 3D Secure doğrulama yapılmıştır</p>
            <p>• Kart bilgileriniz saklanmamaktadır</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <div className="animate-pulse space-y-4">
            <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto"></div>
            <div className="h-8 bg-gray-200 rounded w-3/4 mx-auto"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
          </div>
        </div>
      </div>
    }>
      <PaymentSuccessContent />
    </Suspense>
  )
}
