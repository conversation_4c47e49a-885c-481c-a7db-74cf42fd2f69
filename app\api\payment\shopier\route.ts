import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { getOrder } from '@/lib/orders'
import { createShopierPaymentData, shopierConfig, type ShopierPaymentData } from '@/lib/shopier'
import { supabase, createSupabaseAdmin } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const { orderId } = await request.json()

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      )
    }

    // Temporary: Skip authentication for testing
    const defaultUserId = 'f3821fe6-68e1-43f8-9c7d-3e4162301ffb'

    console.log('Looking for order:', orderId)

    // Get order details (direct query for testing)
    let order
    try {
      // Use admin client to ensure we can read the order
      const supabaseAdmin = createSupabaseAdmin()
      const { data: orders, error: queryError } = await supabaseAdmin
        .from('orders')
        .select(`
          *,
          items:order_items(
            *,
            product:products(*)
          )
        `)
        .eq('id', orderId)

      console.log('Direct query result:', { orders, queryError })

      if (queryError) {
        console.error('Query error:', queryError)
        return NextResponse.json(
          { error: 'Database query failed' },
          { status: 500 }
        )
      }

      if (!orders || orders.length === 0) {
        console.log('No orders found for ID:', orderId)
        return NextResponse.json(
          { error: 'Order not found' },
          { status: 404 }
        )
      }

      order = orders[0]
      console.log('Found order:', order)

    } catch (error) {
      console.error('Error getting order:', error)
      return NextResponse.json(
        { error: 'Failed to get order' },
        { status: 500 }
      )
    }

    // Check if order is already paid
    if (order.status !== 'pending') {
      return NextResponse.json(
        { error: 'Order is not pending payment' },
        { status: 400 }
      )
    }

    // Calculate user account age (days since registration) - default for testing
    const userAccountAge = 30 // Default 30 days for testing

    // Prepare Shopier payment data
    const shopierPaymentData: ShopierPaymentData = {
      orderId: order.id,
      userId: order.user_id,
      userEmail: order.shipping_address.email || '<EMAIL>',
      userFirstName: order.shipping_address.firstName || '',
      userLastName: order.shipping_address.lastName || '',
      userPhone: order.shipping_address.phone || '',
      userAccountAge,
      billingAddress: {
        address: order.billing_address.address || '',
        city: order.billing_address.city || '',
        country: order.billing_address.country || 'Türkiye',
        postcode: order.billing_address.postalCode || ''
      },
      shippingAddress: {
        address: order.shipping_address.address || '',
        city: order.shipping_address.city || '',
        country: order.shipping_address.country || 'Türkiye',
        postcode: order.shipping_address.postalCode || ''
      },
      products: order.items?.map((item: any) => ({
        id: item.product_id,
        name: item.product?.name || 'Product',
        quantity: item.quantity,
        price: item.price,
        discountPrice: undefined, // Can be added if you have discount logic
        subtotalPrice: item.price * item.quantity,
        totalPrice: item.price * item.quantity,
        productType: 0 // 0 for physical products (3D printed items)
      })) || [],
      orderInfo: {
        subtotal: order.subtotal,
        shippingCost: order.shipping_amount || 0,
        total: order.total_amount,
        currency: 'TRY', // Default to Turkish Lira
        discountTotal: 0,
        taxTotal: order.tax_amount || 0
      },
      useShippingAddress: false // Use billing address by default
    }

    // Generate Shopier form data
    const formData = createShopierPaymentData(shopierPaymentData)

    return NextResponse.json({
      success: true,
      data: {
        paymentUrl: shopierConfig.paymentUrl,
        formData
      }
    })

  } catch (error) {
    console.error('Shopier payment API error:', error)
    return NextResponse.json(
      { error: 'Failed to create payment' },
      { status: 500 }
    )
  }
}
