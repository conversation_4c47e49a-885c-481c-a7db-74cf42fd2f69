'use client'

import { useState, useEffect } from 'react'
import { useR<PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { toast } from 'react-hot-toast'
import { 
  ArrowLeft, 
  Package, 
  User, 
  MapPin, 
  CreditCard, 
  Truck,
  CheckCircle,
  XCircle,
  Clock,
  Loader2
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Card,  CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export default function OrderDetailPage() {
  const router = useRouter()
  const params = useParams()
  const orderId = params.id as string
  
  const [order, setOrder] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isUpdating, setIsUpdating] = useState(false)

  useEffect(() => {
    if (orderId) {
      loadOrder()
    }
  }, [orderId])

  const loadOrder = async () => {
    try {
      const response = await fetch(`/api/admin/orders/${orderId}`)
      if (!response.ok) {
        throw new Error('Order not found')
      }
      
      const result = await response.json()
      setOrder(result.data)
    } catch (error) {
      console.error('Error loading order:', error)
      toast.error('Sipariş yüklenemedi')
      router.push('/admin/orders')
    } finally {
      setIsLoading(false)
    }
  }

  const handleStatusUpdate = async (newStatus: string) => {
    setIsUpdating(true)
    try {
      const response = await fetch(`/api/admin/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: newStatus
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update order status')
      }

      const result = await response.json()
      
      if (result.success) {
        toast.success('Sipariş durumu güncellendi')
        setOrder(result.data)
      } else {
        throw new Error(result.error || 'Failed to update order status')
      }
    } catch (error) {
      console.error('Error updating order status:', error)
      toast.error('Sipariş durumu güncellenemedi')
    } finally {
      setIsUpdating(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'Beklemede', variant: 'secondary' as const, icon: Clock },
      confirmed: { label: 'Onaylandı', variant: 'default' as const, icon: CheckCircle },
      shipped: { label: 'Kargoda', variant: 'default' as const, icon: Truck },
      delivered: { label: 'Teslim Edildi', variant: 'default' as const, icon: CheckCircle },
      cancelled: { label: 'İptal Edildi', variant: 'destructive' as const, icon: XCircle }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    const Icon = config.icon

    return (
      <Badge variant={config.variant} className="flex items-center space-x-1">
        <Icon className="h-3 w-3" />
        <span>{config.label}</span>
      </Badge>
    )
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('tr-TR')
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Sipariş yükleniyor...</span>
        </div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Sipariş bulunamadı</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Sipariş #{order.order_number}
            </h1>
            <p className="text-gray-600 mt-2">
              {formatDate(order.created_at)} tarihinde oluşturuldu
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          {getStatusBadge(order.status)}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Order Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Package className="h-5 w-5" />
                <span>Sipariş Ürünleri</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.items?.map((item: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                        <Package className="h-6 w-6 text-gray-400" />
                      </div>
                      <div>
                        <h3 className="font-medium">{item.product?.name || 'Ürün'}</h3>
                        <p className="text-sm text-gray-600">SKU: {item.product?.sku}</p>
                        <p className="text-sm text-gray-600">Adet: {item.quantity}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatPrice(item.price)}</p>
                      <p className="text-sm text-gray-600">
                        Toplam: {formatPrice(item.price * item.quantity)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Müşteri Bilgileri</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">
                    {order.user?.first_name && order.user?.last_name
                      ? `${order.user.first_name} ${order.user.last_name}`
                      : 'Kullanıcı Bilgisi Yok'
                    }
                  </h3>
                  <p className="text-gray-600">{order.user?.email || order.user_id}</p>
                  {order.user?.phone && (
                    <p className="text-gray-600">{order.user.phone}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Addresses */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Shipping Address */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5" />
                  <span>Teslimat Adresi</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="font-medium">{order.shipping_address?.name}</p>
                  <p className="text-gray-600">{order.shipping_address?.address}</p>
                  <p className="text-gray-600">
                    {order.shipping_address?.city}, {order.shipping_address?.postal_code}
                  </p>
                  <p className="text-gray-600">{order.shipping_address?.country}</p>
                  {order.shipping_address?.phone && (
                    <p className="text-gray-600">Tel: {order.shipping_address.phone}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Billing Address */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CreditCard className="h-5 w-5" />
                  <span>Fatura Adresi</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="font-medium">{order.billing_address?.name}</p>
                  <p className="text-gray-600">{order.billing_address?.address}</p>
                  <p className="text-gray-600">
                    {order.billing_address?.city}, {order.billing_address?.postal_code}
                  </p>
                  <p className="text-gray-600">{order.billing_address?.country}</p>
                  {order.billing_address?.phone && (
                    <p className="text-gray-600">Tel: {order.billing_address.phone}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Sipariş Özeti</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span>Ara Toplam:</span>
                <span>{formatPrice(order.subtotal || 0)}</span>
              </div>
              <div className="flex justify-between">
                <span>Kargo:</span>
                <span>{formatPrice(order.shipping_amount || 0)}</span>
              </div>
              <div className="flex justify-between">
                <span>Vergi:</span>
                <span>{formatPrice(order.tax_amount || 0)}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-bold text-lg">
                <span>Toplam:</span>
                <span>{formatPrice(order.total_amount || 0)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Status Update */}
          <Card>
            <CardHeader>
              <CardTitle>Durum Güncelle</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Select 
                value={order.status} 
                onValueChange={handleStatusUpdate}
                disabled={isUpdating}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Durum seçiniz" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Beklemede</SelectItem>
                  <SelectItem value="confirmed">Onaylandı</SelectItem>
                  <SelectItem value="shipped">Kargoda</SelectItem>
                  <SelectItem value="delivered">Teslim Edildi</SelectItem>
                  <SelectItem value="cancelled">İptal Edildi</SelectItem>
                </SelectContent>
              </Select>
              
              {isUpdating && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Güncelleniyor...</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Order Notes */}
          {order.notes && (
            <Card>
              <CardHeader>
                <CardTitle>Sipariş Notları</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">{order.notes}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
