'use client'

import { useEffect, useState, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import ShopierPayment from '@/components/payment/ShopierPayment'
import { getOrder } from '@/lib/orders'
import { getCurrentUser } from '@/lib/auth'
import { toast } from 'react-hot-toast'
import { ArrowLeft, CreditCard, Shield, Lock } from 'lucide-react'
import Link from 'next/link'

function ShopierPaymentContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [order, setOrder] = useState<any>(null)
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const orderId = searchParams.get('order_id')

  useEffect(() => {
    if (!orderId) {
      router.push('/cart')
      return
    }

    loadOrderAndUser()
  }, [orderId, router])

  const loadOrderAndUser = async () => {
    try {
      // Get current user
      const userData = await getCurrentUser()
      if (!userData) {
        router.push('/auth/login')
        return
      }
      setUser(userData)

      // Get order details
      const orderData = await getOrder(orderId!, userData.id)
      if (!orderData) {
        setError('Sipariş bulunamadı')
        return
      }

      if (orderData.status !== 'pending') {
        setError('Bu sipariş zaten işlenmiş')
        return
      }

      setOrder(orderData)
    } catch (error) {
      console.error('Error loading order:', error)
      setError('Sipariş bilgileri yüklenemedi')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePaymentStart = () => {
    toast.loading('Ödeme sayfasına yönlendiriliyor...', { duration: 2000 })
  }

  const handlePaymentError = (errorMessage: string) => {
    router.push(`/payment/error?order_id=${orderId}&error_message=${encodeURIComponent(errorMessage)}`)
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <Card>
            <CardContent className="p-8">
              <div className="text-red-600 mb-4">
                <CreditCard className="h-12 w-12 mx-auto mb-4" />
                <h2 className="text-xl font-semibold mb-2">Ödeme Hatası</h2>
                <p className="text-gray-600">{error}</p>
              </div>
              <div className="space-x-4">
                <Button asChild variant="outline">
                  <Link href="/cart">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Sepete Dön
                  </Link>
                </Button>
                <Button asChild>
                  <Link href="/">Ana Sayfa</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Geri
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Güvenli Ödeme</h1>
              <p className="text-gray-600">Kredi kartı ile güvenli ödeme yapın</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Order Summary */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Sipariş Özeti</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  {order?.items?.map((item: any) => (
                    <div key={item.id} className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">{item.product_name}</p>
                        <p className="text-sm text-gray-600">
                          {item.quantity} adet × {item.price.toLocaleString('tr-TR')} ₺
                        </p>
                      </div>
                      <span className="font-medium">
                        {(item.quantity * item.price).toLocaleString('tr-TR')} ₺
                      </span>
                    </div>
                  ))}
                </div>

                <div className="border-t pt-4 space-y-2">
                  <div className="flex justify-between">
                    <span>Ara Toplam:</span>
                    <span>{order?.subtotal?.toLocaleString('tr-TR') || '0'} ₺</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Kargo:</span>
                    <span>{(order?.shipping_cost || order?.shipping_amount || 0).toLocaleString('tr-TR')} ₺</span>
                  </div>
                  <div className="flex justify-between font-semibold text-lg border-t pt-2">
                    <span>Toplam:</span>
                    <span>{(order?.total || order?.total_amount || 0).toLocaleString('tr-TR')} ₺</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Security Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="h-5 w-5 text-green-600" />
                  <span>Güvenlik Bilgileri</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center space-x-2">
                    <Lock className="h-4 w-4 text-green-600" />
                    <span>256-bit SSL şifreleme</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4 text-green-600" />
                    <span>3D Secure doğrulama</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CreditCard className="h-4 w-4 text-green-600" />
                    <span>PCI DSS uyumlu</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Lock className="h-4 w-4 text-green-600" />
                    <span>Kart bilgileri saklanmaz</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Payment Form */}
          <div>
            <ShopierPayment
              orderId={order?.id}
              orderTotal={order?.total || order?.total_amount || 0}
              onPaymentStart={handlePaymentStart}
              onPaymentError={handlePaymentError}
              locale="tr"
            />
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <Shield className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <h3 className="font-semibold mb-1">Güvenli Ödeme</h3>
            <p className="text-sm text-gray-600">
              Shopier güvencesi ile güvenli ödeme
            </p>
          </div>
          
          <div className="text-center">
            <CreditCard className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <h3 className="font-semibold mb-1">Tüm Kartlar</h3>
            <p className="text-sm text-gray-600">
              Visa, MasterCard, American Express
            </p>
          </div>
          
          <div className="text-center">
            <Lock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <h3 className="font-semibold mb-1">Gizlilik</h3>
            <p className="text-sm text-gray-600">
              Kart bilgileriniz güvende
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function ShopierPaymentPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    }>
      <ShopierPaymentContent />
    </Suspense>
  )
}
