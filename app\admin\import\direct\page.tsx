'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, AlertTriangle, Play } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface ImportResult {
  success: boolean
  message: string
  details?: {
    totalRows: number
    successfulImports: number
    failedImports: number
    errors: string[]
  }
}

export default function DirectImportPage() {
  const [isImporting, setIsImporting] = useState(false)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)

  const handleDirectImport = async () => {
    setIsImporting(true)
    setImportResult(null)

    try {
      const response = await fetch('/api/admin/import-products-direct', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()
      
      if (response.ok) {
        setImportResult(result)
        toast.success('İçe aktarma tamamlandı!')
      } else {
        setImportResult({
          success: false,
          message: result.error || 'İçe aktarma sırasında hata oluştu'
        })
        toast.error('İçe aktarma başarısız')
      }
    } catch (error) {
      console.error('Import error:', error)
      setImportResult({
        success: false,
        message: 'Sunucu hatası oluştu'
      })
      toast.error('İçe aktarma sırasında hata oluştu')
    } finally {
      setIsImporting(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Doğrudan CSV İçe Aktarma</h1>
        <p className="text-gray-600 mt-2">
          Sunucudaki products.csv dosyasından ürünleri doğrudan sisteme aktarın
        </p>
      </div>

      {/* Import Action */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Play className="mr-2 h-5 w-5" />
            Doğrudan İçe Aktarma
          </CardTitle>
          <CardDescription>
            Bu işlem sunucudaki products.csv dosyasını okuyarak ürünleri sisteme aktaracaktır.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Bu işlem geri alınamaz. Mevcut ürünlerle aynı SKU'ya sahip ürünler atlanacaktır.
              </AlertDescription>
            </Alert>

            <Button
              onClick={handleDirectImport}
              disabled={isImporting}
              size="lg"
              className="w-full"
            >
              {isImporting ? 'İçe Aktarılıyor...' : 'İçe Aktarmaya Başla'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Import Results */}
      {importResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              {importResult.success ? (
                <CheckCircle className="mr-2 h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="mr-2 h-5 w-5 text-red-600" />
              )}
              İçe Aktarma Sonucu
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert className={importResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              <AlertDescription>
                {importResult.message}
              </AlertDescription>
            </Alert>

            {importResult.details && (
              <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {importResult.details.totalRows}
                  </div>
                  <div className="text-sm text-gray-600">Toplam Satır</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {importResult.details.successfulImports}
                  </div>
                  <div className="text-sm text-gray-600">Başarılı</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {importResult.details.failedImports}
                  </div>
                  <div className="text-sm text-gray-600">Başarısız</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {Math.round((importResult.details.successfulImports / importResult.details.totalRows) * 100)}%
                  </div>
                  <div className="text-sm text-gray-600">Başarı Oranı</div>
                </div>
              </div>
            )}

            {importResult.details?.errors && importResult.details.errors.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium text-red-800 mb-2 flex items-center">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Hatalar:
                </h4>
                <div className="bg-red-50 border border-red-200 rounded p-3 max-h-40 overflow-y-auto">
                  {importResult.details.errors.slice(0, 10).map((error, index) => (
                    <div key={index} className="text-sm text-red-700 mb-1">
                      {error}
                    </div>
                  ))}
                  {importResult.details.errors.length > 10 && (
                    <div className="text-sm text-red-600 font-medium">
                      ... ve {importResult.details.errors.length - 10} hata daha
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
