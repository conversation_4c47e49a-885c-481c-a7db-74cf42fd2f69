'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { getCart, updateCartItem, removeFromCart } from '@/lib/cart'
import { getCurrentUser } from '@/lib/auth'
import { toast } from 'react-hot-toast'
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Trash2, 
  ArrowLeft,
  ArrowRight
} from 'lucide-react'

export default function CartPage() {
  const [cartItems, setCartItems] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [updatingItems, setUpdatingItems] = useState<Set<string>>(new Set())
  const [shippingCost, setShippingCost] = useState(29.90)
  const [freeShippingThreshold, setFreeShippingThreshold] = useState(500)

  useEffect(() => {
    loadCart()
    loadShippingSettings()
  }, [])

  const loadShippingSettings = async () => {
    try {
      const settingsResponse = await fetch('/api/admin/settings/shipping')
      if (settingsResponse.ok) {
        const settingsData = await settingsResponse.json()
        setShippingCost(settingsData.data.shipping_cost.amount)
        setFreeShippingThreshold(settingsData.data.free_shipping_threshold.amount)
      }
    } catch (error) {
      console.error('Error loading shipping settings:', error)
    }
  }

  const loadCart = async () => {
    try {
      const user = await getCurrentUser()
      if (!user) {
        setIsLoading(false)
        return
      }

      const items = await getCart(user.id)
      setCartItems(items)
    } catch (error) {
      console.error('Error loading cart:', error)
      toast.error('Sepet yüklenemedi')
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdateQuantity = async (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      handleRemoveItem(itemId)
      return
    }

    setUpdatingItems(prev => new Set(prev).add(itemId))
    try {
      await updateCartItem(itemId, newQuantity)
      setCartItems(prev => 
        prev.map(item => 
          item.id === itemId 
            ? { ...item, quantity: newQuantity }
            : item
        )
      )
      toast.success('Miktar güncellendi')
    } catch (error) {
      toast.error('Miktar güncellenemedi')
    } finally {
      setUpdatingItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(itemId)
        return newSet
      })
    }
  }

  const handleRemoveItem = async (itemId: string) => {
    setUpdatingItems(prev => new Set(prev).add(itemId))
    try {
      await removeFromCart(itemId)
      setCartItems(prev => prev.filter(item => item.id !== itemId))
      toast.success('Ürün sepetten çıkarıldı')
    } catch (error) {
      toast.error('Ürün çıkarılamadı')
    } finally {
      setUpdatingItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(itemId)
        return newSet
      })
    }
  }

  const calculateSubtotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0)
  }

  const calculateShipping = () => {
    const subtotal = calculateSubtotal()
    return subtotal >= freeShippingThreshold ? 0 : shippingCost
  }

  const calculateTotal = () => {
    return calculateSubtotal() + calculateShipping()
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-8">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex space-x-4 p-4 border rounded-lg">
                  <div className="w-20 h-20 bg-gray-200 rounded"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
        <Link href="/" className="hover:text-gray-900">Ana Sayfa</Link>
        <span>/</span>
        <span className="text-gray-900">Sepetim</span>
      </nav>

      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Sepetim</h1>
        <Button asChild variant="outline">
          <Link href="/products">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Alışverişe Devam Et
          </Link>
        </Button>
      </div>

      {cartItems.length === 0 ? (
        <div className="text-center py-16">
          <ShoppingCart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">Sepetiniz Boş</h2>
          <p className="text-gray-600 mb-8">
            Henüz sepetinize ürün eklemediniz. Hemen alışverişe başlayın!
          </p>
          <Button asChild size="lg">
            <Link href="/products">
              Ürünleri Keşfet
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Sepetinizdeki Ürünler ({cartItems.length})</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {cartItems.map((item) => {
                  const isUpdating = updatingItems.has(item.id)
                  const primaryImage = item.product.images?.find(img => img.is_primary) || item.product.images?.[0]
                  
                  return (
                    <div key={item.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                      {/* Product Image */}
                      <div className="relative w-20 h-20 flex-shrink-0">
                        {primaryImage ? (
                          <Image
                            src={primaryImage.image_url}
                            alt={item.product.name}
                            fill
                            className="object-cover rounded"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center">
                            <ShoppingCart className="h-8 w-8 text-gray-400" />
                          </div>
                        )}
                      </div>

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <Link 
                          href={`/products/${item.product.slug}`}
                          className="font-medium text-gray-900 hover:text-blue-600 block truncate"
                        >
                          {item.product.name}
                        </Link>
                        <p className="text-sm text-gray-600 mt-1">
                          SKU: {item.product.sku}
                        </p>
                        <p className="text-lg font-semibold text-gray-900 mt-2">
                          {item.price.toLocaleString('tr-TR')} ₺
                        </p>
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                          disabled={isUpdating || item.quantity <= 1}
                          className="h-8 w-8"
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <span className="w-12 text-center font-medium">
                          {item.quantity}
                        </span>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                          disabled={isUpdating}
                          className="h-8 w-8"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* Item Total */}
                      <div className="text-right">
                        <p className="font-semibold text-gray-900">
                          {(item.price * item.quantity).toLocaleString('tr-TR')} ₺
                        </p>
                      </div>

                      {/* Remove Button */}
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveItem(item.id)}
                        disabled={isUpdating}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )
                })}
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle>Sipariş Özeti</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Ara Toplam:</span>
                  <span>{calculateSubtotal().toLocaleString('tr-TR')} ₺</span>
                </div>
                
                <div className="flex justify-between">
                  <span>Kargo:</span>
                  <span>
                    {calculateShipping() === 0 ? (
                      <span className="text-green-600 font-medium">Ücretsiz</span>
                    ) : (
                      `${calculateShipping().toLocaleString('tr-TR')} ₺`
                    )}
                  </span>
                </div>

                {calculateShipping() > 0 && (
                  <p className="text-sm text-gray-600">
                    {(freeShippingThreshold - calculateSubtotal()).toLocaleString('tr-TR')} ₺ daha alışveriş yapın,
                    kargo ücretsiz olsun!
                  </p>
                )}

                <Separator />

                <div className="flex justify-between text-lg font-semibold">
                  <span>Toplam:</span>
                  <span>{calculateTotal().toLocaleString('tr-TR')} ₺</span>
                </div>

                <Button className="w-full" size="lg" asChild>
                  <Link href="/checkout">
                    Siparişi Tamamla
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>

                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <span>✓</span>
                    <span>Güvenli ödeme</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span>✓</span>
                    <span>Hızlı teslimat</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span>✓</span>
                    <span>Kolay iade</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  )
}
