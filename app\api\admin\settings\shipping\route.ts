import { NextRequest, NextResponse } from 'next/server'
import { getShippingSettings, updateShippingSettings } from '@/lib/settings'

export async function GET(request: NextRequest) {
  try {
    const settings = await getShippingSettings()
    
    return NextResponse.json({
      success: true,
      data: settings
    })
  } catch (error) {
    console.error('Error fetching shipping settings:', error)
    return NextResponse.json(
      { error: 'Kargo ayarları yüklenemedi' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate the request body
    if (!body.shipping_cost || !body.free_shipping_threshold) {
      return NextResponse.json(
        { error: 'Gerekli alan<PERSON> eks<PERSON>' },
        { status: 400 }
      )
    }

    // Validate shipping cost
    if (typeof body.shipping_cost.amount !== 'number' || body.shipping_cost.amount < 0) {
      return NextResponse.json(
        { error: 'Geçersiz kargo ücreti' },
        { status: 400 }
      )
    }

    // Validate free shipping threshold
    if (typeof body.free_shipping_threshold.amount !== 'number' || body.free_shipping_threshold.amount < 0) {
      return NextResponse.json(
        { error: 'Geçersiz ücretsiz kargo limiti' },
        { status: 400 }
      )
    }

    await updateShippingSettings({
      shipping_cost: body.shipping_cost,
      free_shipping_threshold: body.free_shipping_threshold,
      shipping_enabled: body.shipping_enabled
    })
    
    return NextResponse.json({
      success: true,
      message: 'Kargo ayarları başarıyla güncellendi'
    })
  } catch (error) {
    console.error('Error updating shipping settings:', error)
    return NextResponse.json(
      { error: 'Kargo ayarları güncellenemedi' },
      { status: 500 }
    )
  }
}
