import { NextRequest, NextResponse } from 'next/server'
import { updateOrderStatus } from '@/lib/orders'
import { processShopierCallback, type ShopierCallbackData } from '@/lib/shopier'

export async function POST(request: NextRequest) {
  try {
    // Parse form data from Shopier callback
    const formData = await request.formData()
    
    const callbackData: ShopierCallbackData = {
      platform_order_id: formData.get('platform_order_id') as string,
      status: formData.get('status') as string,
      payment_id: formData.get('payment_id') as string,
      installment: formData.get('installment') as string,
      random_nr: formData.get('random_nr') as string,
      signature: formData.get('signature') as string,
      error_message: formData.get('error_message') as string
    }

    // Validate required fields
    if (!callbackData.platform_order_id || !callbackData.status || !callbackData.signature) {
      console.error('Missing required callback data:', callbackData)
      return NextResponse.json(
        { error: 'Missing required callback data' },
        { status: 400 }
      )
    }

    // Process the callback
    const result = processShopierCallback(callbackData)

    if (!result.success) {
      console.error('Shopier callback failed:', result.errorMessage)
      
      // Update order status to failed
      try {
        await updateOrderStatus(result.orderId, 'cancelled')
      } catch (error) {
        console.error('Failed to update order status to cancelled:', error)
      }

      return NextResponse.json(
        { error: result.errorMessage },
        { status: 400 }
      )
    }

    // Payment successful - update order
    try {
      await updateOrderStatus(result.orderId, 'confirmed')
      
      // You can also store payment details in order metadata
      // await updateOrderPaymentDetails(result.orderId, {
      //   payment_id: result.paymentId,
      //   installment: result.installment,
      //   payment_method: 'shopier'
      // })

      console.log('Shopier payment successful:', {
        orderId: result.orderId,
        paymentId: result.paymentId,
        installment: result.installment
      })

      return NextResponse.json({
        success: true,
        message: 'Payment processed successfully'
      })

    } catch (error) {
      console.error('Failed to update order after successful payment:', error)
      return NextResponse.json(
        { error: 'Failed to update order' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Shopier callback API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle GET requests (for testing or verification)
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Shopier callback endpoint is active',
    timestamp: new Date().toISOString()
  })
}
