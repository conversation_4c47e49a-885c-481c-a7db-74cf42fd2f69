'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  UserProfileDropdown,
  UserProfileDropdownItem,
  UserProfileDropdownSeparator,
  UserProfileHeader
} from '@/components/ui/user-profile-dropdown'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import {
  Search,
  ShoppingCart,
  User,
  Menu,
  LogOut,
  Settings,
  Package,
  Heart,
  Printer,
} from 'lucide-react'
import { ThemeSelector } from '@/components/theme/ThemeSelector'
import { getCurrentUser, signOut } from '@/lib/auth'
import { getCart } from '@/lib/cart'
import { getCategories } from '@/lib/products'
import { supabase } from '@/lib/supabase'
import { toast } from 'react-hot-toast'

export default function Header() {
  const [user, setUser] = useState<any>(null)
  const [cartItemCount, setCartItemCount] = useState(0)
  const [categories, setCategories] = useState<any[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    loadUserData()
    loadCategories()

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id)

        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          // User signed in, reload user data with a small delay
          // This gives time for profile creation to complete
          setTimeout(async () => {
            await loadUserData()
          }, 500)
        } else if (event === 'SIGNED_OUT') {
          // User signed out, clear state
          setUser(null)
          setCartItemCount(0)
        }
      }
    )

    // Cleanup subscription on unmount
    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const loadUserData = async () => {
    try {
      const userData = await getCurrentUser()
      setUser(userData)
      
      if (userData) {
        const cartItems = await getCart(userData.id)
        setCartItemCount(cartItems.reduce((sum, item) => sum + item.quantity, 0))
      }
    } catch (error) {
      console.error('Error loading user data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadCategories = async () => {
    try {
      const data = await getCategories()
      setCategories(data)
    } catch (error) {
      console.error('Error loading categories:', error)
    }
  }

  const handleSignOut = async () => {
    try {
      await signOut()
      setUser(null)
      setCartItemCount(0)
      toast.success('Çıkış yapıldı')
      router.push('/')
      router.refresh()
    } catch (error) {
      toast.error('Çıkış yapılırken hata oluştu')
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`)
    }
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      {/* Top Bar */}
      <div className="bg-blue-600 text-white text-sm">
        <div className="container mx-auto px-4 py-2 text-center">
          <span>Ücretsiz kargo 500₺ üzeri alışverişlerde! 🚚</span>
        </div>
      </div>

      {/* Main Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Mobile Menu */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col space-y-4 mt-4">
                <Link href="/products" className="text-lg font-medium">
                  Tüm Ürünler
                </Link>
                {categories.map((category) => (
                  <Link
                    key={category.id}
                    href={`/category/${category.slug}`}
                    className="text-lg font-medium"
                  >
                    {category.name}
                  </Link>
                ))}
              </div>
            </SheetContent>
          </Sheet>

          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="bg-blue-600 p-2 rounded-lg">
              <Printer className="h-6 w-6 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">3dunyam</span>
          </Link>

          {/* Search Bar */}
          <form onSubmit={handleSearch} className="hidden md:flex flex-1 max-w-lg mx-8">
            <div className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Ürün, kategori veya marka ara..."
                className="pl-10 w-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </form>

          {/* Right Actions */}
          <div className="flex items-center space-x-4">
            {/* Theme Selector */}
            <ThemeSelector />

            {/* Cart */}
            <Link href="/cart">
              <Button variant="ghost" size="icon" className="relative">
                <ShoppingCart className="h-5 w-5" />
                {cartItemCount > 0 && (
                  <Badge className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs bg-red-500">
                    {cartItemCount}
                  </Badge>
                )}
              </Button>
            </Link>

            {/* User Menu */}
            {user ? (
              <UserProfileDropdown
                trigger={
                  <Button variant="ghost" size="icon" className="relative">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-semibold text-xs">
                        {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                      </span>
                    </div>
                  </Button>
                }
              >
                <UserProfileHeader user={user} />
                <div className="flex flex-col space-y-1 p-1">
                  <UserProfileDropdownItem href="/profile">
                    <Settings className="mr-3 h-5 w-5 text-zinc-500" />
                    <span>Profil</span>
                  </UserProfileDropdownItem>
                  <UserProfileDropdownItem href="/orders">
                    <Package className="mr-3 h-5 w-5 text-zinc-500" />
                    <span>Siparişlerim</span>
                  </UserProfileDropdownItem>
                  <UserProfileDropdownItem href="/wishlist">
                    <Heart className="mr-3 h-5 w-5 text-zinc-500" />
                    <span>Favorilerim</span>
                  </UserProfileDropdownItem>
                  {user.role === 'admin' && (
                    <UserProfileDropdownItem href="/admin">
                      <Settings className="mr-3 h-5 w-5 text-zinc-500" />
                      <span>Admin Panel</span>
                    </UserProfileDropdownItem>
                  )}
                </div>
                <UserProfileDropdownSeparator />
                <div className="flex flex-col space-y-1 p-1">
                  <UserProfileDropdownItem onClick={handleSignOut}>
                    <LogOut className="mr-3 h-5 w-5 text-zinc-500" />
                    <span>Çıkış Yap</span>
                  </UserProfileDropdownItem>
                </div>
              </UserProfileDropdown>
            ) : (
              <div className="flex items-center space-x-2">
                <Button asChild variant="ghost" size="sm">
                  <Link href="/auth/login">Giriş</Link>
                </Button>
                <Button asChild size="sm" className="bg-blue-600 hover:bg-blue-700">
                  <Link href="/auth/register">Kayıt Ol</Link>
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Mobile Search */}
        <form onSubmit={handleSearch} className="mt-4 md:hidden">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="search"
              placeholder="Ürün ara..."
              className="pl-10 w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </form>

        {/* Navigation */}
        <nav className="hidden md:flex items-center space-x-8 mt-4 pt-4 border-t">
          <Link
            href="/products"
            className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
          >
            Tüm Ürünler
          </Link>
          {categories.slice(0, 5).map((category) => (
            <Link
              key={category.id}
              href={`/category/${category.slug}`}
              className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
            >
              {category.name}
            </Link>
          ))}
        </nav>
      </div>
    </header>
  )
}