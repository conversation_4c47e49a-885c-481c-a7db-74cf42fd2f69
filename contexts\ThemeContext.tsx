'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { Theme, themes, getThemeById, getDefaultTheme } from '@/lib/theme'
import { saveThemeToStorage, loadThemeFromStorage, isStorageAvailable } from '@/lib/theme-storage'

interface ThemeContextType {
  currentTheme: Theme
  setTheme: (themeId: string) => void
  availableThemes: Theme[]
  isLoading: boolean
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

interface ThemeProviderProps {
  children: React.ReactNode
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState<Theme>(getDefaultTheme())
  const [isLoading, setIsLoading] = useState(true)

  // Load theme from localStorage on mount
  useEffect(() => {
    const loadTheme = () => {
      if (!isStorageAvailable()) {
        applyThemeToDocument(currentTheme)
        setIsLoading(false)
        return
      }

      const savedThemeId = loadThemeFromStorage()
      if (savedThemeId) {
        const theme = getThemeById(savedThemeId)
        if (theme) {
          setCurrentTheme(theme)
          applyThemeToDocument(theme)
        } else {
          // Invalid theme ID in storage, use default
          applyThemeToDocument(currentTheme)
        }
      } else {
        // No saved theme, use default
        applyThemeToDocument(currentTheme)
      }

      setIsLoading(false)
    }

    // Small delay to ensure DOM is ready
    const timeoutId = setTimeout(loadTheme, 100)
    return () => clearTimeout(timeoutId)
  }, [])

  const applyThemeToDocument = (theme: Theme) => {
    const root = document.documentElement
    
    // Remove existing theme classes
    themes.forEach(t => {
      root.classList.remove(t.name)
    })
    
    // Add new theme class
    if (theme.name !== 'default') {
      root.classList.add(theme.name)
    }

    // Apply CSS custom properties
    Object.entries(theme.colors).forEach(([key, value]) => {
      const cssVarName = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`
      root.style.setProperty(cssVarName, value)
    })
  }

  const setTheme = (themeId: string) => {
    const theme = getThemeById(themeId)
    if (theme) {
      setCurrentTheme(theme)
      applyThemeToDocument(theme)

      // Save to localStorage
      saveThemeToStorage(themeId)
    }
  }

  const value: ThemeContextType = {
    currentTheme,
    setTheme,
    availableThemes: themes,
    isLoading
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}
