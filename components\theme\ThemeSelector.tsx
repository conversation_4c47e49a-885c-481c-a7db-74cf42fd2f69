'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Palette, Check } from 'lucide-react'
import { useTheme } from '@/contexts/ThemeContext'

export const ThemeSelector: React.FC = () => {
  const { currentTheme, setTheme, availableThemes, isLoading } = useTheme()

  if (isLoading) {
    return (
      <Button variant="ghost" size="icon" disabled>
        <Palette className="h-5 w-5" />
      </Button>
    )
  }

  const getThemeIcon = (themeId: string) => {
    switch (themeId) {
      case 'default':
        return '☀️'
      case 'dark':
        return '🌙'
      case 'blue-ocean':
        return '🌊'
      case 'green-forest':
        return '🌲'
      case 'purple-galaxy':
        return '🌌'
      default:
        return '🎨'
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Palette className="h-5 w-5" />
          <span className="sr-only"><PERSON><PERSON></span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {availableThemes.map((theme) => (
          <DropdownMenuItem
            key={theme.id}
            onClick={() => setTheme(theme.id)}
            className="flex items-center justify-between cursor-pointer"
          >
            <div className="flex items-center space-x-2">
              <span className="text-lg">{getThemeIcon(theme.id)}</span>
              <div className="flex flex-col">
                <span className="font-medium">{theme.displayName}</span>
                <span className="text-xs text-muted-foreground">
                  {theme.description}
                </span>
              </div>
            </div>
            {currentTheme.id === theme.id && (
              <Check className="h-4 w-4 text-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default ThemeSelector
