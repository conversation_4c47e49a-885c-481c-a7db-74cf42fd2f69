import { NextRequest, NextResponse } from 'next/server'
import { getOrder, updateOrderStatus, cancelOrder } from '@/lib/orders'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const orderId = id
    
    // Get order details (admin can see any order)
    const order = await getOrder(orderId)
    
    return NextResponse.json({ 
      success: true, 
      data: order 
    })
  } catch (error) {
    console.error('Order fetch API error:', error)
    return NextResponse.json(
      { error: 'Sipariş bulunamadı' },
      { status: 404 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const orderId = id
    const { status, tracking_number } = await request.json()
    
    if (!status) {
      return NextResponse.json(
        { error: 'Durum gerekli' },
        { status: 400 }
      )
    }

    // Update order status
    const updatedOrder = await updateOrderStatus(orderId, status)
    
    // TODO: Handle tracking_number if needed
    
    return NextResponse.json({ 
      success: true, 
      data: updatedOrder,
      message: 'Sipariş durumu güncellendi' 
    })
  } catch (error) {
    console.error('Order update API error:', error)
    return NextResponse.json(
      { error: 'Sipariş güncellenemedi' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const orderId = id
    
    // Cancel order (soft delete)
    const cancelledOrder = await cancelOrder(orderId)
    
    return NextResponse.json({ 
      success: true, 
      data: cancelledOrder,
      message: 'Sipariş iptal edildi' 
    })
  } catch (error) {
    console.error('Order cancellation API error:', error)
    return NextResponse.json(
      { error: 'Sipariş iptal edilemedi' },
      { status: 500 }
    )
  }
}
