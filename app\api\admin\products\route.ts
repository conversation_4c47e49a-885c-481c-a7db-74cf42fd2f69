import { NextRequest, NextResponse } from 'next/server'
import { createProduct, createProductImage, createInventory } from '@/lib/products'

export async function POST(request: NextRequest) {
  try {
    const productData = await request.json()
    
    // Create product
    const newProduct = await createProduct({
      name: productData.name,
      slug: productData.slug,
      description: productData.description,
      short_description: productData.short_description,
      price: productData.price,
      compare_price: productData.compare_price,
      sku: productData.sku,
      category_id: productData.category_id,
      material_type: productData.material_type,
      dimensions: productData.dimensions,
      weight_grams: productData.weight_grams,
      is_active: productData.is_active,
      is_featured: productData.is_featured,
      seo_title: productData.seo_title,
      seo_description: productData.seo_description
    })

    // Create product images
    if (productData.images && productData.images.length > 0) {
      for (const imageData of productData.images) {
        await createProductImage({
          product_id: newProduct.id,
          image_url: imageData.image_url,
          alt_text: imageData.alt_text,
          sort_order: imageData.sort_order,
          is_primary: imageData.is_primary
        })
      }
    }

    // Create inventory
    if (productData.inventory) {
      await createInventory({
        product_id: newProduct.id,
        quantity: productData.inventory.quantity,
        reserved_quantity: productData.inventory.reserved_quantity,
        reorder_level: productData.inventory.reorder_level
      })
    }

    return NextResponse.json({ 
      success: true, 
      product: newProduct 
    })
  } catch (error) {
    console.error('Product creation API error:', error)
    return NextResponse.json(
      { error: 'Ürün oluşturulamadı' },
      { status: 500 }
    )
  }
}
