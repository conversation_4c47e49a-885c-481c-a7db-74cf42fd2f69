# Technical Architecture Document

## 1. System Overview

### 1.1 Architecture Pattern
3Dünyam platform follows a **JAMstack architecture** with server-side rendering capabilities:

```
┌─────────────────────────────────────────────────────────────┐
│                    Client Layer                             │
├─────────────────────────────────────────────────────────────┤
│  Next.js Frontend (React + TypeScript + Tailwind CSS)      │
│  - Server Components                                        │
│  - Client Components                                        │
│  - Static Generation                                        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    API Layer                                │
├─────────────────────────────────────────────────────────────┤
│  Next.js API Routes                                         │
│  - Public APIs (products, categories)                      │
│  - Authenticated APIs (cart, orders)                       │
│  - Admin APIs (management)                                 │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  Business Logic Layer                       │
├─────────────────────────────────────────────────────────────┤
│  Service Functions                                          │
│  - Product Service                                          │
│  - Order Service                                            │
│  - Inventory Service                                        │
│  - Auth Service                                             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Data Layer                               │
├─────────────────────────────────────────────────────────────┤
│  Supabase (PostgreSQL + Auth + Storage)                    │
│  - Row Level Security (RLS)                                │
│  - Real-time subscriptions                                 │
│  - File storage                                            │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 Technology Stack

#### Frontend
- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui
- **State Management**: React hooks + Context API
- **Form Handling**: React Hook Form
- **Validation**: Zod

#### Backend
- **Runtime**: Node.js
- **API**: Next.js API Routes
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth
- **File Storage**: Supabase Storage
- **Real-time**: Supabase Realtime

#### Development Tools
- **Package Manager**: Bun
- **Code Quality**: ESLint + Prettier
- **Type Checking**: TypeScript
- **Testing**: Jest + React Testing Library
- **Deployment**: Vercel

## 2. Database Architecture

### 2.1 Entity Relationship Diagram

```mermaid
erDiagram
    profiles ||--o{ orders : places
    profiles ||--o{ carts : has
    categories ||--o{ products : contains
    products ||--o{ product_images : has
    products ||--|| inventory : tracks
    products ||--o{ cart_items : contains
    products ||--o{ order_items : contains
    carts ||--o{ cart_items : contains
    orders ||--o{ order_items : contains

    profiles {
        uuid id PK
        text email
        text first_name
        text last_name
        text phone
        user_role role
        text avatar_url
        text address_line1
        text address_line2
        text city
        text postal_code
        text country
        timestamptz created_at
        timestamptz updated_at
    }

    categories {
        uuid id PK
        text name
        text slug
        text description
        text image_url
        boolean is_active
        int sort_order
        timestamptz created_at
        timestamptz updated_at
    }

    products {
        uuid id PK
        text name
        text slug
        text description
        text short_description
        decimal price
        decimal compare_price
        text sku
        uuid category_id FK
        text material_type
        text dimensions
        int weight_grams
        boolean is_active
        boolean is_featured
        text seo_title
        text seo_description
        timestamptz created_at
        timestamptz updated_at
    }

    product_images {
        uuid id PK
        uuid product_id FK
        text image_url
        text alt_text
        int sort_order
        boolean is_primary
        timestamptz created_at
    }

    inventory {
        uuid id PK
        uuid product_id FK
        int quantity
        int reserved_quantity
        int reorder_level
        timestamptz updated_at
    }

    carts {
        uuid id PK
        uuid user_id FK
        timestamptz created_at
        timestamptz updated_at
    }

    cart_items {
        uuid id PK
        uuid cart_id FK
        uuid product_id FK
        int quantity
        decimal price
        timestamptz created_at
        timestamptz updated_at
    }

    orders {
        uuid id PK
        text order_number
        uuid user_id FK
        order_status status
        decimal subtotal
        decimal shipping_cost
        decimal total
        jsonb shipping_address
        jsonb billing_address
        text payment_method
        text notes
        timestamptz created_at
        timestamptz updated_at
    }

    order_items {
        uuid id PK
        uuid order_id FK
        uuid product_id FK
        int quantity
        decimal price
        text product_name
        text product_sku
        timestamptz created_at
    }
```

### 2.2 Database Constraints & Indexes

#### Primary Keys
- All tables use UUID primary keys for better distribution and security

#### Foreign Keys
- Proper referential integrity maintained
- Cascade deletes where appropriate

#### Indexes
```sql
-- Performance indexes
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_slug ON products(slug);
CREATE INDEX idx_products_sku ON products(sku);
CREATE INDEX idx_products_active ON products(is_active);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_cart_items_cart_id ON cart_items(cart_id);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);

-- Composite indexes
CREATE INDEX idx_products_active_featured ON products(is_active, is_featured);
CREATE INDEX idx_orders_user_status ON orders(user_id, status);
```

### 2.3 Row Level Security (RLS) Policies

```sql
-- Profiles: Users can only access their own profile
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- Products: Public read access
CREATE POLICY "Anyone can view active products" ON products
    FOR SELECT USING (is_active = true);

-- Orders: Users can only access their own orders
CREATE POLICY "Users can view own orders" ON orders
    FOR SELECT USING (auth.uid() = user_id);

-- Admin policies
CREATE POLICY "Admins can manage all data" ON products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
```

## 3. API Architecture

### 3.1 API Design Principles

1. **RESTful Design**: Standard HTTP methods and status codes
2. **Consistent Response Format**: Standardized JSON responses
3. **Error Handling**: Comprehensive error responses
4. **Authentication**: JWT-based authentication
5. **Rate Limiting**: Protection against abuse
6. **Validation**: Input validation on all endpoints

### 3.2 API Endpoints Structure

```
/api/
├── auth/
│   ├── login
│   ├── register
│   └── logout
├── products/
│   ├── GET /           # List products
│   ├── GET /[slug]     # Get product by slug
│   └── GET /search     # Search products
├── categories/
│   └── GET /           # List categories
├── cart/
│   ├── GET /           # Get user cart
│   ├── POST /          # Add to cart
│   ├── PUT /[id]       # Update cart item
│   └── DELETE /[id]    # Remove from cart
├── orders/
│   ├── GET /           # List user orders
│   ├── POST /          # Create order
│   └── GET /[id]       # Get order details
└── admin/
    ├── products/
    │   ├── GET /           # List all products
    │   ├── POST /          # Create product
    │   ├── PUT /[id]       # Update product
    │   └── DELETE /[id]    # Delete product
    ├── orders/
    │   ├── GET /           # List all orders
    │   └── PUT /[id]       # Update order status
    ├── inventory/
    │   └── PUT /[id]       # Update inventory
    └── stats/
        └── GET /           # Get dashboard stats
```

### 3.3 Response Format Standards

#### Success Response
```typescript
interface SuccessResponse<T> {
  success: true
  data: T
  message?: string
  meta?: {
    total?: number
    page?: number
    limit?: number
  }
}
```

#### Error Response
```typescript
interface ErrorResponse {
  success: false
  error: {
    code: string
    message: string
    details?: any
  }
}
```

## 4. Security Architecture

### 4.1 Authentication Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant A as API
    participant S as Supabase Auth
    participant D as Database

    C->>A: Login Request
    A->>S: Validate Credentials
    S->>A: JWT Token
    A->>C: Return Token + User Data
    
    Note over C: Store token in httpOnly cookie
    
    C->>A: Authenticated Request
    A->>S: Verify JWT
    S->>A: User Claims
    A->>D: Query with RLS
    D->>A: Filtered Data
    A->>C: Response
```

### 4.2 Authorization Levels

1. **Public**: No authentication required
   - Product catalog
   - Category listing
   - Product search

2. **Authenticated**: Valid JWT required
   - Cart operations
   - Order placement
   - Profile management

3. **Admin**: Admin role required
   - Product management
   - Order management
   - Inventory management
   - Dashboard access

### 4.3 Data Protection

- **Encryption**: All data encrypted at rest and in transit
- **Input Validation**: Comprehensive validation using Zod schemas
- **SQL Injection Prevention**: Parameterized queries via Supabase
- **XSS Prevention**: Content sanitization
- **CSRF Protection**: SameSite cookies and CSRF tokens

## 5. Performance Architecture

### 5.1 Caching Strategy

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Browser       │    │   CDN/Edge      │    │   Server        │
│   Cache         │    │   Cache         │    │   Cache         │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Static assets │    │ • Images        │    │ • API responses │
│ • API responses │    │ • CSS/JS        │    │ • Database      │
│ • User data     │    │ • Static pages  │    │   queries       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 5.2 Optimization Techniques

1. **Static Generation**: Pre-build static pages for better performance
2. **Image Optimization**: Next.js Image component with lazy loading
3. **Code Splitting**: Automatic code splitting by Next.js
4. **Database Optimization**: Proper indexing and query optimization
5. **CDN**: Static asset delivery via CDN

### 5.3 Monitoring & Observability

- **Error Tracking**: Sentry integration
- **Performance Monitoring**: Web Vitals tracking
- **Database Monitoring**: Supabase dashboard
- **API Monitoring**: Response time and error rate tracking

## 6. Deployment Architecture

### 6.1 Environment Strategy

```
Development → Staging → Production
     ↓           ↓         ↓
   Local DB   Test DB   Prod DB
```

### 6.2 CI/CD Pipeline

```mermaid
graph LR
    A[Code Push] --> B[GitHub Actions]
    B --> C[Build & Test]
    C --> D[Deploy to Vercel]
    D --> E[Run E2E Tests]
    E --> F[Production Ready]
```

### 6.3 Infrastructure

- **Hosting**: Vercel (Frontend + API)
- **Database**: Supabase (Managed PostgreSQL)
- **CDN**: Vercel Edge Network
- **DNS**: Vercel DNS
- **SSL**: Automatic SSL certificates

## 7. Scalability Considerations

### 7.1 Horizontal Scaling
- Stateless API design
- Database connection pooling
- CDN for static assets

### 7.2 Vertical Scaling
- Database performance tuning
- Query optimization
- Caching strategies

### 7.3 Future Considerations
- Microservices architecture
- Event-driven architecture
- Separate read/write databases

---

**Document Version**: 1.0  
**Last Updated**: 2024-01-24  
**Next Review**: 2024-02-24
